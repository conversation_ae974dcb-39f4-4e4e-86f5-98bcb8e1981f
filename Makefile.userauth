# Makefile for Headlamp User Authentication System
# This file contains targets for building, testing, and managing the user authentication system

.PHONY: help build-userauth test-userauth clean-userauth install-userauth-tools

# Default target
help:
	@echo "Headlamp User Authentication System"
	@echo "=================================="
	@echo ""
	@echo "Available targets:"
	@echo "  build-userauth        - Build the user authentication system"
	@echo "  build-config-gen      - Build the userpass-config-gen tool"
	@echo "  test-userauth         - Run user authentication tests"
	@echo "  test-userauth-unit    - Run unit tests only"
	@echo "  test-userauth-integration - Run integration tests"
	@echo "  install-userauth-tools - Install user authentication tools"
	@echo "  clean-userauth        - Clean user authentication build artifacts"
	@echo "  create-test-config    - Create a test user configuration"
	@echo "  validate-config       - Validate user configuration file"
	@echo "  help                  - Show this help message"
	@echo ""
	@echo "Environment variables:"
	@echo "  CONFIG_FILE          - Path to user configuration file (default: /tmp/test-users.yaml)"
	@echo "  HEADLAMP_URL         - Headlamp server URL (default: http://localhost:4466)"

# Configuration
CONFIG_FILE ?= /tmp/test-users.yaml
HEADLAMP_URL ?= http://localhost:4466
GO_BUILD_FLAGS ?= -v
TEST_FLAGS ?= -v -race

# Build targets
build-userauth:
	@echo "Building Headlamp with user authentication support..."
	cd backend && go build $(GO_BUILD_FLAGS) -o ../headlamp-server ./cmd/
	@echo "Build completed: headlamp-server"

build-config-gen:
	@echo "Building userpass-config-gen tool..."
	cd cmd/userpass-config-gen && go build $(GO_BUILD_FLAGS) -o ../../userpass-config-gen .
	@echo "Build completed: userpass-config-gen"

# Test targets
test-userauth: test-userauth-unit test-userauth-integration

test-userauth-unit:
	@echo "Running user authentication unit tests..."
	cd backend && go test $(TEST_FLAGS) ./pkg/userauth/...

test-userauth-integration:
	@echo "Running user authentication integration tests..."
	@if [ ! -f headlamp-server ]; then \
		echo "Error: headlamp-server not found. Run 'make build-userauth' first."; \
		exit 1; \
	fi
	@chmod +x scripts/test-user-auth.sh
	./scripts/test-user-auth.sh

# Installation targets
install-userauth-tools: build-config-gen
	@echo "Installing user authentication tools..."
	sudo cp userpass-config-gen /usr/local/bin/
	sudo chmod +x /usr/local/bin/userpass-config-gen
	@echo "Tools installed to /usr/local/bin/"

# Configuration management
create-test-config: build-config-gen
	@echo "Creating test user configuration..."
	@mkdir -p $(dir $(CONFIG_FILE))
	@echo "Creating test users..."
	@echo "users:" > $(CONFIG_FILE)
	@echo "  - username: admin" >> $(CONFIG_FILE)
	@echo "    passwordHash: \$$2a\$$12\$$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL9B8L3Sm" >> $(CONFIG_FILE)
	@echo "    serviceAccount: headlamp-admin" >> $(CONFIG_FILE)
	@echo "    namespace: kube-system" >> $(CONFIG_FILE)
	@echo "    enabled: true" >> $(CONFIG_FILE)
	@echo "    description: \"Test admin user (password: admin123)\"" >> $(CONFIG_FILE)
	@echo "    createdAt: $$(date -u +\"%Y-%m-%dT%H:%M:%SZ\")" >> $(CONFIG_FILE)
	@echo "  - username: developer" >> $(CONFIG_FILE)
	@echo "    passwordHash: \$$2a\$$12\$$9y8Nd2j3kL4mN5oP6qR7sT8uV9wX0yZ1aB2cD3eF4gH5iJ6kL7mN8o" >> $(CONFIG_FILE)
	@echo "    serviceAccount: headlamp-developer" >> $(CONFIG_FILE)
	@echo "    namespace: default" >> $(CONFIG_FILE)
	@echo "    enabled: true" >> $(CONFIG_FILE)
	@echo "    description: \"Test developer user (password: dev123)\"" >> $(CONFIG_FILE)
	@echo "    createdAt: $$(date -u +\"%Y-%m-%dT%H:%M:%SZ\")" >> $(CONFIG_FILE)
	@echo "Test configuration created at $(CONFIG_FILE)"
	@echo ""
	@echo "Test users:"
	@echo "  admin / admin123 (bound to headlamp-admin SA)"
	@echo "  developer / dev123 (bound to headlamp-developer SA)"

validate-config: build-config-gen
	@echo "Validating user configuration..."
	@if [ ! -f $(CONFIG_FILE) ]; then \
		echo "Error: Configuration file $(CONFIG_FILE) not found."; \
		echo "Run 'make create-test-config' to create a test configuration."; \
		exit 1; \
	fi
	./userpass-config-gen --action=validate --config=$(CONFIG_FILE)

# Kubernetes setup
setup-test-rbac:
	@echo "Setting up test RBAC resources..."
	kubectl apply -f - <<EOF
	apiVersion: v1
	kind: ServiceAccount
	metadata:
	  name: headlamp-admin
	  namespace: kube-system
	---
	apiVersion: rbac.authorization.k8s.io/v1
	kind: ClusterRoleBinding
	metadata:
	  name: headlamp-admin
	roleRef:
	  apiGroup: rbac.authorization.k8s.io
	  kind: ClusterRole
	  name: cluster-admin
	subjects:
	- kind: ServiceAccount
	  name: headlamp-admin
	  namespace: kube-system
	---
	apiVersion: v1
	kind: ServiceAccount
	metadata:
	  name: headlamp-developer
	  namespace: default
	---
	apiVersion: rbac.authorization.k8s.io/v1
	kind: ClusterRoleBinding
	metadata:
	  name: headlamp-developer
	roleRef:
	  apiGroup: rbac.authorization.k8s.io
	  kind: ClusterRole
	  name: edit
	subjects:
	- kind: ServiceAccount
	  name: headlamp-developer
	  namespace: default
	EOF
	@echo "Test RBAC resources created"

cleanup-test-rbac:
	@echo "Cleaning up test RBAC resources..."
	kubectl delete serviceaccount headlamp-admin -n kube-system --ignore-not-found=true
	kubectl delete clusterrolebinding headlamp-admin --ignore-not-found=true
	kubectl delete serviceaccount headlamp-developer -n default --ignore-not-found=true
	kubectl delete clusterrolebinding headlamp-developer --ignore-not-found=true
	@echo "Test RBAC resources cleaned up"

# Development targets
run-with-userauth: build-userauth create-test-config setup-test-rbac
	@echo "Starting Headlamp with user authentication..."
	@echo "Configuration file: $(CONFIG_FILE)"
	@echo "Server URL: $(HEADLAMP_URL)"
	@echo ""
	@echo "Test users:"
	@echo "  admin / admin123"
	@echo "  developer / dev123"
	@echo ""
	./headlamp-server \
		--enable-user-auth \
		--user-auth-config-file=$(CONFIG_FILE) \
		--user-auth-jwt-secret=test-secret-key-for-development \
		--user-auth-token-expiration=24h \
		--dev

# Cleanup targets
clean-userauth:
	@echo "Cleaning up user authentication build artifacts..."
	rm -f headlamp-server
	rm -f userpass-config-gen
	rm -f $(CONFIG_FILE)
	@echo "Cleanup completed"

# Documentation targets
docs-userauth:
	@echo "User Authentication Documentation"
	@echo "==============================="
	@echo ""
	@echo "Configuration file format:"
	@cat examples/users.yaml | head -20
	@echo ""
	@echo "For complete documentation, see: docs/user-authentication.md"

# Utility targets
list-users: build-config-gen
	@if [ ! -f $(CONFIG_FILE) ]; then \
		echo "Error: Configuration file $(CONFIG_FILE) not found."; \
		exit 1; \
	fi
	./userpass-config-gen --action=list --config=$(CONFIG_FILE)

add-user: build-config-gen
	@if [ -z "$(USERNAME)" ]; then \
		echo "Error: USERNAME is required. Usage: make add-user USERNAME=newuser"; \
		exit 1; \
	fi
	./userpass-config-gen --action=add --username=$(USERNAME) --config=$(CONFIG_FILE)

remove-user: build-config-gen
	@if [ -z "$(USERNAME)" ]; then \
		echo "Error: USERNAME is required. Usage: make remove-user USERNAME=olduser"; \
		exit 1; \
	fi
	./userpass-config-gen --action=remove --username=$(USERNAME) --config=$(CONFIG_FILE)

# Check dependencies
check-deps:
	@echo "Checking dependencies..."
	@command -v go >/dev/null 2>&1 || { echo "Error: Go is required but not installed."; exit 1; }
	@command -v kubectl >/dev/null 2>&1 || { echo "Error: kubectl is required but not installed."; exit 1; }
	@command -v curl >/dev/null 2>&1 || { echo "Error: curl is required but not installed."; exit 1; }
	@echo "All dependencies are available"

# Full setup for development
dev-setup: check-deps build-userauth build-config-gen create-test-config setup-test-rbac
	@echo ""
	@echo "Development setup completed!"
	@echo ""
	@echo "To start Headlamp with user authentication:"
	@echo "  make run-with-userauth"
	@echo ""
	@echo "To run tests:"
	@echo "  make test-userauth"
	@echo ""
	@echo "To manage users:"
	@echo "  make list-users"
	@echo "  make add-user USERNAME=newuser"
	@echo "  make remove-user USERNAME=olduser"
