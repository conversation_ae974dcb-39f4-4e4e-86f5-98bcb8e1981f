# Headlamp 用户认证系统测试指南

## 测试状态

✅ **语法验证通过** - 所有代码文件语法正确  
✅ **文件完整性检查通过** - 所有必需文件已创建  
✅ **集成点检查通过** - 后端集成正确  
✅ **文档完整** - 所有文档已创建  

## 测试方法

### 1. 语法验证测试（已完成）

```bash
./scripts/syntax-check.sh
```

**结果**: ✅ 通过 - 所有语法检查都成功

### 2. 构建测试（需要 Go 环境）

```bash
# 安装 Go 后运行
./scripts/build-test.sh

# 或使用 Makefile
make -f Makefile.userauth build-userauth
make -f Makefile.userauth build-config-gen
```

### 3. 单元测试（需要 Go 环境）

```bash
# 运行用户认证包的单元测试
make -f Makefile.userauth test-userauth-unit

# 或直接使用 Go
cd backend && go test -v ./pkg/userauth/...
```

### 4. 集成测试（需要 Go + Kubernetes）

```bash
# 完整的集成测试
make -f Makefile.userauth dev-setup
make -f Makefile.userauth run-with-userauth

# 在另一个终端运行测试
make -f Makefile.userauth test-userauth-integration
```

### 5. 手动 API 测试

```bash
# 1. 启动服务器（需要先构建）
./headlamp-server \
  --enable-user-auth \
  --user-auth-config-file=/tmp/test-users.yaml \
  --user-auth-jwt-secret=test-secret \
  --user-auth-token-expiration=24h

# 2. 测试认证状态
curl http://localhost:4466/api/auth/status

# 3. 测试用户登录
curl -X POST http://localhost:4466/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 4. 使用 token 访问 API
curl http://localhost:4466/api/clusters/default/api/v1/pods \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 测试用户

系统预配置了以下测试用户：

| 用户名 | 密码 | ServiceAccount | 权限级别 |
|--------|------|----------------|----------|
| admin | admin123 | headlamp-admin | cluster-admin |
| developer | dev123 | headlamp-developer | edit |

## 预期测试结果

### 认证状态 API
```json
{
  "enabled": true,
  "authenticated": false
}
```

### 登录成功响应
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresAt": "2025-01-02T00:00:00Z",
  "user": {
    "username": "admin",
    "serviceAccount": "headlamp-admin",
    "namespace": "kube-system"
  }
}
```

### API 访问成功
- HTTP 状态码: 200
- 返回 Kubernetes API 数据

## 故障排除

### 常见问题

1. **Go 未安装**
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install golang-go
   
   # 或下载最新版本
   wget https://go.dev/dl/go1.21.0.linux-amd64.tar.gz
   sudo tar -C /usr/local -xzf go1.21.0.linux-amd64.tar.gz
   export PATH=$PATH:/usr/local/go/bin
   ```

2. **Kubernetes 集群未连接**
   ```bash
   # 检查 kubectl 连接
   kubectl cluster-info
   
   # 如果没有集群，可以使用 minikube
   minikube start
   ```

3. **ServiceAccount 不存在**
   ```bash
   # 创建测试 RBAC 资源
   make -f Makefile.userauth setup-test-rbac
   ```

4. **端口冲突**
   ```bash
   # 检查端口 4466 是否被占用
   lsof -i :4466
   
   # 或使用不同端口
   ./headlamp-server --listen-addr=localhost:8080
   ```

### 调试模式

启用详细日志：
```bash
export HEADLAMP_LOG_LEVEL=debug
./headlamp-server --enable-user-auth
```

## 测试环境要求

### 最小要求（语法检查）
- ✅ Linux/macOS/Windows
- ✅ Bash shell
- ✅ 基本命令行工具 (grep, awk, etc.)

### 构建测试要求
- Go 1.19+ 
- 网络连接（下载依赖）

### 集成测试要求
- Go 1.19+
- Kubernetes 集群访问
- kubectl 配置
- curl 命令

### 完整测试要求
- 以上所有要求
- Docker（可选，用于容器化测试）

## 下一步

### 如果所有测试通过

1. **前端集成**
   - 修改前端登录界面
   - 添加用户名密码输入表单
   - 集成 JWT token 管理

2. **生产部署**
   - 配置安全的 JWT 密钥
   - 设置用户配置文件权限
   - 配置 HTTPS
   - 设置监控和日志

3. **功能扩展**
   - 添加用户管理界面
   - 实现密码修改功能
   - 添加审计日志
   - 集成外部认证系统

### 如果测试失败

1. **检查错误日志**
   - 查看具体错误信息
   - 检查依赖是否正确安装

2. **验证环境**
   - 确认 Go 版本兼容
   - 检查 Kubernetes 连接
   - 验证权限设置

3. **逐步调试**
   - 从语法检查开始
   - 逐个运行测试步骤
   - 查看详细错误输出

## 测试报告模板

```
测试日期: ___________
测试环境: ___________
Go 版本: ___________
Kubernetes 版本: ___________

测试结果:
[ ] 语法验证
[ ] 构建测试  
[ ] 单元测试
[ ] 集成测试
[ ] 手动 API 测试

问题记录:
_________________________
_________________________

建议:
_________________________
_________________________
```

## 联系和支持

如果在测试过程中遇到问题：

1. 检查 [IMPLEMENTATION-SUMMARY.md](IMPLEMENTATION-SUMMARY.md) 了解实现详情
2. 查看 [docs/user-authentication.md](docs/user-authentication.md) 获取完整文档
3. 运行 `./scripts/syntax-check.sh` 进行基本验证
4. 查看错误日志获取详细信息

---

**注意**: 本测试指南基于当前实现的用户认证系统。在生产环境中使用前，请确保所有安全配置都已正确设置。
