# Headlamp 用户名密码认证系统

本文档介绍如何测试和使用 Headlamp 的用户名密码认证系统，该系统将用户绑定到 Kubernetes ServiceAccount。

## 功能概述

- ✅ 用户名密码认证
- ✅ 用户与 Kubernetes ServiceAccount 绑定
- ✅ JWT 会话管理
- ✅ 密码安全哈希 (bcrypt)
- ✅ 基于 RBAC 的权限控制
- ✅ 配置文件管理
- ✅ 命令行工具支持

## 快速开始

### 1. 构建系统

```bash
# 构建 Headlamp 服务器（包含用户认证功能）
make -f Makefile.userauth build-userauth

# 构建用户配置管理工具
make -f Makefile.userauth build-config-gen
```

### 2. 创建测试配置

```bash
# 创建测试用户配置文件
make -f Makefile.userauth create-test-config

# 设置测试 RBAC 资源
make -f Makefile.userauth setup-test-rbac
```

### 3. 启动服务器

```bash
# 启动带用户认证的 Headlamp 服务器
make -f Makefile.userauth run-with-userauth
```

### 4. 运行测试

```bash
# 运行所有测试
make -f Makefile.userauth test-userauth

# 仅运行单元测试
make -f Makefile.userauth test-userauth-unit

# 仅运行集成测试
make -f Makefile.userauth test-userauth-integration
```

## 测试用户

默认创建的测试用户：

| 用户名 | 密码 | ServiceAccount | 权限 |
|--------|------|----------------|------|
| admin | admin123 | headlamp-admin | cluster-admin |
| developer | dev123 | headlamp-developer | edit |

## 手动测试步骤

### 1. 检查认证状态

```bash
curl http://localhost:4466/api/auth/status
```

预期响应：
```json
{
  "enabled": true,
  "authenticated": false
}
```

### 2. 用户登录

```bash
curl -X POST http://localhost:4466/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

预期响应：
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresAt": "2025-01-02T00:00:00Z",
  "user": {
    "username": "admin",
    "serviceAccount": "headlamp-admin",
    "namespace": "kube-system"
  }
}
```

### 3. 使用 Token 访问 API

```bash
# 保存 token
TOKEN="your-jwt-token-here"

# 访问 Kubernetes API
curl http://localhost:4466/api/clusters/default/api/v1/pods \
  -H "Authorization: Bearer $TOKEN"
```

### 4. 刷新 Token

```bash
curl -X POST http://localhost:4466/api/auth/refresh \
  -H "Authorization: Bearer $TOKEN"
```

### 5. 登出

```bash
curl -X POST http://localhost:4466/api/auth/logout \
  -H "Authorization: Bearer $TOKEN"
```

## 用户管理

### 添加新用户

```bash
# 交互式添加用户
./userpass-config-gen --action=add --username=newuser --config=/tmp/test-users.yaml

# 或使用 Makefile
make -f Makefile.userauth add-user USERNAME=newuser
```

### 列出所有用户

```bash
./userpass-config-gen --action=list --config=/tmp/test-users.yaml

# 或使用 Makefile
make -f Makefile.userauth list-users
```

### 删除用户

```bash
./userpass-config-gen --action=remove --username=olduser --config=/tmp/test-users.yaml

# 或使用 Makefile
make -f Makefile.userauth remove-user USERNAME=olduser
```

### 验证配置

```bash
./userpass-config-gen --action=validate --config=/tmp/test-users.yaml

# 或使用 Makefile
make -f Makefile.userauth validate-config
```

## 配置文件格式

用户配置文件 (`users.yaml`) 格式：

```yaml
users:
  - username: admin
    passwordHash: $2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL9B8L3Sm
    serviceAccount: headlamp-admin
    namespace: kube-system
    enabled: true
    description: "系统管理员"
    createdAt: 2025-01-01T00:00:00Z
```

## 命令行参数

启动 Headlamp 时的用户认证相关参数：

```bash
./headlamp-server \
  --enable-user-auth \                              # 启用用户认证
  --user-auth-config-file=/etc/headlamp/users.yaml \  # 用户配置文件路径
  --user-auth-jwt-secret=your-secret-key \          # JWT 签名密钥
  --user-auth-token-expiration=24h                  # Token 过期时间
```

## 环境变量

也可以使用环境变量配置：

```bash
export HEADLAMP_CONFIG_ENABLE_USER_AUTH=true
export HEADLAMP_CONFIG_USER_AUTH_CONFIG_FILE=/etc/headlamp/users.yaml
export HEADLAMP_CONFIG_USER_AUTH_JWT_SECRET=your-secret-key
export HEADLAMP_CONFIG_USER_AUTH_TOKEN_EXPIRATION=24h
```

## 故障排除

### 常见问题

1. **认证失败**
   - 检查用户名密码是否正确
   - 确认用户账户已启用
   - 验证配置文件格式

2. **ServiceAccount 未找到**
   - 确认 ServiceAccount 存在于指定命名空间
   - 检查 RBAC 绑定

3. **权限被拒绝**
   - 检查 ServiceAccount 的 RBAC 绑定
   - 确认用户有足够的权限

4. **Token 过期**
   - 使用刷新端点获取新 token
   - 或重新登录

### 调试日志

启用调试日志：

```bash
export HEADLAMP_LOG_LEVEL=debug
./headlamp-server --enable-user-auth
```

### 配置验证

```bash
# 验证配置文件
make -f Makefile.userauth validate-config

# 检查依赖
make -f Makefile.userauth check-deps
```

## 安全注意事项

1. **文件权限**：设置用户配置文件权限为 600
   ```bash
   chmod 600 /etc/headlamp/users.yaml
   ```

2. **JWT 密钥**：使用强随机密钥
   ```bash
   openssl rand -base64 32
   ```

3. **HTTPS**：生产环境中始终使用 HTTPS

4. **密码策略**：强制使用强密码（最少 8 个字符）

5. **定期轮换**：定期轮换密码和 JWT 密钥

## 架构说明

```
用户登录 (用户名/密码)
    ↓
Headlamp 后端验证
    ↓
JWT 会话 Token 生成
    ↓
ServiceAccount Token 获取
    ↓
使用 SA Token 访问 Kubernetes API
```

## 下一步

1. 测试完成后，您可以：
   - 修改前端以支持用户名密码登录界面
   - 添加更多用户管理功能
   - 集成到现有的身份验证系统
   - 添加审计日志功能

2. 生产部署建议：
   - 使用外部数据库存储用户信息
   - 集成 LDAP/AD 认证
   - 添加多因素认证
   - 实现密码策略和过期机制

## 相关文档

- [完整用户认证文档](docs/user-authentication.md)
- [示例配置文件](examples/users.yaml)
- [API 端点文档](docs/user-authentication.md#api-endpoints)
