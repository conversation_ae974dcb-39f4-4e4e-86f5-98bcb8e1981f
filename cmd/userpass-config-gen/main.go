/*
Copyright 2025 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package main

import (
	"bufio"
	"flag"
	"fmt"
	"os"
	"strings"
	"syscall"
	"time"

	"github.com/kubernetes-sigs/headlamp/backend/pkg/userauth"
	"golang.org/x/term"
)

func main() {
	var (
		configFile = flag.String("config", "/etc/headlamp/users.yaml", "Path to user configuration file")
		action     = flag.String("action", "generate", "Action to perform: generate, add, remove, list, validate")
		username   = flag.String("username", "", "Username for add/remove actions")
	)
	flag.Parse()

	switch *action {
	case "generate":
		if err := userauth.GenerateUserConfig(*configFile); err != nil {
			fmt.Fprintf(os.Stderr, "Error generating config: %v\n", err)
			os.Exit(1)
		}

	case "add":
		if *username != "" {
			user, err := promptForUserDetails(*username)
			if err != nil {
				fmt.Fprintf(os.Stderr, "Error getting user details: %v\n", err)
				os.Exit(1)
			}
			if err := userauth.AddUserToConfig(*configFile, user); err != nil {
				fmt.Fprintf(os.Stderr, "Error adding user: %v\n", err)
				os.Exit(1)
			}
			fmt.Printf("User %s added successfully.\n", *username)
		} else {
			fmt.Fprintf(os.Stderr, "Username is required for add action\n")
			os.Exit(1)
		}

	case "remove":
		if *username != "" {
			if err := userauth.RemoveUserFromConfig(*configFile, *username); err != nil {
				fmt.Fprintf(os.Stderr, "Error removing user: %v\n", err)
				os.Exit(1)
			}
			fmt.Printf("User %s removed successfully.\n", *username)
		} else {
			fmt.Fprintf(os.Stderr, "Username is required for remove action\n")
			os.Exit(1)
		}

	case "list":
		if err := userauth.ListUsers(*configFile); err != nil {
			fmt.Fprintf(os.Stderr, "Error listing users: %v\n", err)
			os.Exit(1)
		}

	case "validate":
		if err := userauth.ValidateConfig(*configFile); err != nil {
			fmt.Fprintf(os.Stderr, "Validation failed: %v\n", err)
			os.Exit(1)
		}

	default:
		fmt.Fprintf(os.Stderr, "Unknown action: %s\n", *action)
		fmt.Fprintf(os.Stderr, "Available actions: generate, add, remove, list, validate\n")
		os.Exit(1)
	}
}

func promptForUserDetails(username string) (*userauth.User, error) {
	scanner := bufio.NewScanner(os.Stdin)
	
	user := &userauth.User{
		Username:  username,
		Enabled:   true,
		CreatedAt: time.Now(),
	}

	// Password
	fmt.Print("Password: ")
	passwordBytes, err := term.ReadPassword(int(syscall.Stdin))
	if err != nil {
		return nil, fmt.Errorf("failed to read password: %w", err)
	}
	fmt.Println() // New line after password input

	password := string(passwordBytes)
	if len(password) < userauth.MinPasswordLength {
		return nil, fmt.Errorf("password must be at least %d characters", userauth.MinPasswordLength)
	}

	// Hash password
	hasher := userauth.NewBcryptHasher(userauth.DefaultBcryptCost)
	passwordHash, err := hasher.HashPassword(password)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}
	user.PasswordHash = passwordHash

	// Service Account
	fmt.Print("Service Account name: ")
	scanner.Scan()
	user.ServiceAccount = strings.TrimSpace(scanner.Text())
	if user.ServiceAccount == "" {
		return nil, fmt.Errorf("service account cannot be empty")
	}

	// Namespace
	fmt.Print("Namespace (default: default): ")
	scanner.Scan()
	user.Namespace = strings.TrimSpace(scanner.Text())
	if user.Namespace == "" {
		user.Namespace = "default"
	}

	// Description (optional)
	fmt.Print("Description (optional): ")
	scanner.Scan()
	user.Description = strings.TrimSpace(scanner.Text())

	return user, nil
}
