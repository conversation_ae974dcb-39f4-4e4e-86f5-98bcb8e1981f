#!/bin/bash

# Test script for Headlamp user authentication system
# This script tests the backend authentication functionality

set -e

# Configuration
HEADLAMP_URL="http://localhost:4466"
CONFIG_FILE="/tmp/test-users.yaml"
TEST_USERNAME="testuser"
TEST_PASSWORD="testpass123"
TEST_SA="test-serviceaccount"
TEST_NAMESPACE="default"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Headlamp is running
check_headlamp() {
    log_info "Checking if Headlamp is running..."
    if ! curl -s "$HEADLAMP_URL/api/auth/status" > /dev/null; then
        log_error "Headlamp is not running at $HEADLAMP_URL"
        log_info "Please start Headlamp with user authentication enabled:"
        log_info "  ./headlamp-server --enable-user-auth --user-auth-config-file=$CONFIG_FILE"
        exit 1
    fi
    log_info "Headlamp is running"
}

# Create test user configuration
create_test_config() {
    log_info "Creating test user configuration..."
    
    # Generate password hash (using a simple method for testing)
    # In production, use the userpass-config-gen tool
    HASH='$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL9B8L3Sm' # testpass123
    
    cat > "$CONFIG_FILE" << EOF
users:
  - username: $TEST_USERNAME
    passwordHash: $HASH
    serviceAccount: $TEST_SA
    namespace: $TEST_NAMESPACE
    enabled: true
    description: "Test user for authentication testing"
    createdAt: $(date -u +"%Y-%m-%dT%H:%M:%SZ")
EOF
    
    log_info "Test configuration created at $CONFIG_FILE"
}

# Create test ServiceAccount in Kubernetes
create_test_serviceaccount() {
    log_info "Creating test ServiceAccount..."
    
    kubectl apply -f - << EOF
apiVersion: v1
kind: ServiceAccount
metadata:
  name: $TEST_SA
  namespace: $TEST_NAMESPACE
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: $TEST_SA-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: view
subjects:
- kind: ServiceAccount
  name: $TEST_SA
  namespace: $TEST_NAMESPACE
EOF
    
    log_info "ServiceAccount $TEST_SA created with view permissions"
}

# Test authentication status endpoint
test_auth_status() {
    log_info "Testing authentication status endpoint..."
    
    RESPONSE=$(curl -s "$HEADLAMP_URL/api/auth/status")
    echo "Response: $RESPONSE"
    
    if echo "$RESPONSE" | grep -q '"enabled":true'; then
        log_info "✓ User authentication is enabled"
    else
        log_error "✗ User authentication is not enabled"
        return 1
    fi
}

# Test user login
test_login() {
    log_info "Testing user login..."
    
    RESPONSE=$(curl -s -X POST "$HEADLAMP_URL/api/auth/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$TEST_USERNAME\",\"password\":\"$TEST_PASSWORD\"}")
    
    echo "Login response: $RESPONSE"
    
    if echo "$RESPONSE" | grep -q '"token"'; then
        TOKEN=$(echo "$RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
        log_info "✓ Login successful, token received"
        echo "$TOKEN" > /tmp/test-token
        return 0
    else
        log_error "✗ Login failed"
        return 1
    fi
}

# Test token validation
test_token_validation() {
    log_info "Testing token validation..."
    
    if [ ! -f /tmp/test-token ]; then
        log_error "No token found, skipping validation test"
        return 1
    fi
    
    TOKEN=$(cat /tmp/test-token)
    
    RESPONSE=$(curl -s "$HEADLAMP_URL/api/auth/status" \
        -H "Authorization: Bearer $TOKEN")
    
    echo "Status with token: $RESPONSE"
    
    if echo "$RESPONSE" | grep -q '"authenticated":true'; then
        log_info "✓ Token validation successful"
        return 0
    else
        log_error "✗ Token validation failed"
        return 1
    fi
}

# Test API access with token
test_api_access() {
    log_info "Testing API access with token..."
    
    if [ ! -f /tmp/test-token ]; then
        log_error "No token found, skipping API access test"
        return 1
    fi
    
    TOKEN=$(cat /tmp/test-token)
    
    # Try to access pods API (should work with view permissions)
    RESPONSE=$(curl -s -w "%{http_code}" "$HEADLAMP_URL/api/clusters/default/api/v1/pods" \
        -H "Authorization: Bearer $TOKEN")
    
    HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
    
    if [ "$HTTP_CODE" = "200" ]; then
        log_info "✓ API access successful (HTTP $HTTP_CODE)"
        return 0
    else
        log_error "✗ API access failed (HTTP $HTTP_CODE)"
        echo "Response: $RESPONSE"
        return 1
    fi
}

# Test invalid login
test_invalid_login() {
    log_info "Testing invalid login..."
    
    RESPONSE=$(curl -s -w "%{http_code}" -X POST "$HEADLAMP_URL/api/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"username":"invalid","password":"invalid"}')
    
    HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
    
    if [ "$HTTP_CODE" = "401" ]; then
        log_info "✓ Invalid login correctly rejected (HTTP $HTTP_CODE)"
        return 0
    else
        log_error "✗ Invalid login not properly rejected (HTTP $HTTP_CODE)"
        return 1
    fi
}

# Test logout
test_logout() {
    log_info "Testing logout..."
    
    if [ ! -f /tmp/test-token ]; then
        log_error "No token found, skipping logout test"
        return 1
    fi
    
    TOKEN=$(cat /tmp/test-token)
    
    RESPONSE=$(curl -s -w "%{http_code}" -X POST "$HEADLAMP_URL/api/auth/logout" \
        -H "Authorization: Bearer $TOKEN")
    
    HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
    
    if [ "$HTTP_CODE" = "200" ]; then
        log_info "✓ Logout successful (HTTP $HTTP_CODE)"
        return 0
    else
        log_error "✗ Logout failed (HTTP $HTTP_CODE)"
        return 1
    fi
}

# Cleanup
cleanup() {
    log_info "Cleaning up test resources..."
    
    # Remove test files
    rm -f "$CONFIG_FILE" /tmp/test-token
    
    # Remove test ServiceAccount
    kubectl delete serviceaccount "$TEST_SA" -n "$TEST_NAMESPACE" --ignore-not-found=true
    kubectl delete clusterrolebinding "$TEST_SA-binding" --ignore-not-found=true
    
    log_info "Cleanup completed"
}

# Main test execution
main() {
    log_info "Starting Headlamp user authentication tests..."
    
    # Setup
    create_test_config
    create_test_serviceaccount
    
    # Tests
    check_headlamp
    test_auth_status
    test_login
    test_token_validation
    test_api_access
    test_invalid_login
    test_logout
    
    log_info "All tests completed successfully!"
}

# Handle script interruption
trap cleanup EXIT

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    log_error "kubectl is required but not installed"
    exit 1
fi

# Check if curl is available
if ! command -v curl &> /dev/null; then
    log_error "curl is required but not installed"
    exit 1
fi

# Run main function
main "$@"
