#!/bin/bash

# Build test script for Headlamp user authentication system
# This script tests if the code compiles correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check Go installation
check_go() {
    log_info "Checking Go installation..."
    if ! command -v go &> /dev/null; then
        log_error "Go is not installed or not in PATH"
        exit 1
    fi
    
    GO_VERSION=$(go version | awk '{print $3}')
    log_info "Go version: $GO_VERSION"
}

# Test Go module dependencies
test_dependencies() {
    log_info "Testing Go module dependencies..."
    
    cd backend
    
    # Download dependencies
    log_info "Downloading dependencies..."
    go mod download
    
    # Verify dependencies
    log_info "Verifying dependencies..."
    go mod verify
    
    # Tidy up
    log_info "Tidying up dependencies..."
    go mod tidy
    
    cd ..
}

# Test compilation of userauth package
test_userauth_package() {
    log_info "Testing userauth package compilation..."
    
    cd backend
    
    # Test compilation without building
    go build -o /dev/null ./pkg/userauth/...
    
    log_info "✓ userauth package compiles successfully"
    
    cd ..
}

# Test compilation of main server
test_server_compilation() {
    log_info "Testing server compilation..."
    
    cd backend
    
    # Test compilation
    go build -o ../headlamp-server-test ./cmd/
    
    if [ -f ../headlamp-server-test ]; then
        log_info "✓ Server compiles successfully"
        rm ../headlamp-server-test
    else
        log_error "✗ Server compilation failed"
        exit 1
    fi
    
    cd ..
}

# Test compilation of config generator
test_config_gen_compilation() {
    log_info "Testing config generator compilation..."
    
    cd cmd/userpass-config-gen
    
    # Test compilation
    go build -o ../../userpass-config-gen-test .
    
    if [ -f ../../userpass-config-gen-test ]; then
        log_info "✓ Config generator compiles successfully"
        rm ../../userpass-config-gen-test
    else
        log_error "✗ Config generator compilation failed"
        exit 1
    fi
    
    cd ../..
}

# Run unit tests
test_unit_tests() {
    log_info "Running unit tests..."
    
    cd backend
    
    # Run tests for userauth package
    go test -v ./pkg/userauth/...
    
    log_info "✓ Unit tests passed"
    
    cd ..
}

# Test imports and syntax
test_syntax() {
    log_info "Testing Go syntax and imports..."
    
    # Check backend code
    cd backend
    
    # Format check
    if ! gofmt -l . | grep -q .; then
        log_info "✓ Backend code is properly formatted"
    else
        log_warn "Backend code formatting issues found:"
        gofmt -l .
    fi
    
    # Vet check
    go vet ./...
    log_info "✓ Backend code passes go vet"
    
    cd ..
    
    # Check config generator
    cd cmd/userpass-config-gen
    
    go vet .
    log_info "✓ Config generator passes go vet"
    
    cd ../..
}

# Main test execution
main() {
    log_info "Starting Headlamp user authentication build tests..."
    
    check_go
    test_dependencies
    test_syntax
    test_userauth_package
    test_server_compilation
    test_config_gen_compilation
    test_unit_tests
    
    log_info "All build tests completed successfully!"
    log_info ""
    log_info "Next steps:"
    log_info "1. Build the system: make -f Makefile.userauth build-userauth"
    log_info "2. Create test config: make -f Makefile.userauth create-test-config"
    log_info "3. Run integration tests: make -f Makefile.userauth test-userauth-integration"
}

# Run main function
main "$@"
