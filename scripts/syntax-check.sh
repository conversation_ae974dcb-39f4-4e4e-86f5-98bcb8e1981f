#!/bin/bash

# Syntax check script for Headlamp user authentication system
# This script performs basic syntax validation without requiring Go installation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if files exist
check_files() {
    log_info "Checking if all required files exist..."
    
    local files=(
        "backend/pkg/userauth/types.go"
        "backend/pkg/userauth/password.go"
        "backend/pkg/userauth/jwt.go"
        "backend/pkg/userauth/serviceaccount.go"
        "backend/pkg/userauth/manager.go"
        "backend/pkg/userauth/handlers.go"
        "backend/pkg/userauth/cli.go"
        "backend/pkg/userauth/password_test.go"
        "backend/pkg/userauth/jwt_test.go"
        "cmd/userpass-config-gen/main.go"
        "examples/users.yaml"
        "docs/user-authentication.md"
        "README-UserAuth.md"
        "Makefile.userauth"
        "scripts/test-user-auth.sh"
    )
    
    local missing_files=()
    
    for file in "${files[@]}"; do
        if [ ! -f "$file" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -eq 0 ]; then
        log_info "✓ All required files exist"
    else
        log_error "✗ Missing files:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        return 1
    fi
}

# Check Go syntax (basic)
check_go_syntax() {
    log_info "Checking Go file syntax..."
    
    local go_files=(
        "backend/pkg/userauth/types.go"
        "backend/pkg/userauth/password.go"
        "backend/pkg/userauth/jwt.go"
        "backend/pkg/userauth/serviceaccount.go"
        "backend/pkg/userauth/manager.go"
        "backend/pkg/userauth/handlers.go"
        "backend/pkg/userauth/cli.go"
        "cmd/userpass-config-gen/main.go"
    )
    
    local syntax_errors=()
    
    for file in "${go_files[@]}"; do
        # Basic syntax checks
        if ! grep -q "^package " "$file"; then
            syntax_errors+=("$file: Missing package declaration")
        fi
        
        # Check for basic Go syntax patterns
        if grep -q "func.*{$" "$file"; then
            # Check for matching braces (very basic)
            local open_braces=$(grep -o "{" "$file" | wc -l)
            local close_braces=$(grep -o "}" "$file" | wc -l)
            if [ "$open_braces" -ne "$close_braces" ]; then
                syntax_errors+=("$file: Mismatched braces (${open_braces} open, ${close_braces} close)")
            fi
        fi
        
        # Check for common syntax errors
        if grep -q "import.*(" "$file"; then
            if ! grep -q ")" "$file"; then
                syntax_errors+=("$file: Unclosed import statement")
            fi
        fi
    done
    
    if [ ${#syntax_errors[@]} -eq 0 ]; then
        log_info "✓ Basic Go syntax checks passed"
    else
        log_error "✗ Syntax errors found:"
        for error in "${syntax_errors[@]}"; do
            echo "  - $error"
        done
        return 1
    fi
}

# Check imports and dependencies
check_imports() {
    log_info "Checking Go imports..."
    
    local required_imports=(
        "golang.org/x/crypto"
        "golang.org/x/term"
        "gopkg.in/yaml.v2"
    )
    
    # Check if go.mod has required dependencies
    if [ -f "backend/go.mod" ]; then
        for import in "${required_imports[@]}"; do
            if ! grep -q "$import" "backend/go.mod"; then
                log_warn "Missing dependency in go.mod: $import"
            fi
        done
        log_info "✓ Go module dependencies checked"
    else
        log_error "✗ backend/go.mod not found"
        return 1
    fi
}

# Check configuration files
check_config_files() {
    log_info "Checking configuration files..."
    
    # Check YAML syntax (basic)
    if [ -f "examples/users.yaml" ]; then
        if grep -q "users:" "examples/users.yaml"; then
            log_info "✓ Example users.yaml has correct structure"
        else
            log_error "✗ Example users.yaml missing 'users:' key"
            return 1
        fi
    fi
    
    # Check Makefile syntax (basic)
    if [ -f "Makefile.userauth" ]; then
        if grep -q "build-userauth:" "Makefile.userauth"; then
            log_info "✓ Makefile.userauth has build targets"
        else
            log_error "✗ Makefile.userauth missing build targets"
            return 1
        fi
    fi
}

# Check documentation
check_documentation() {
    log_info "Checking documentation..."
    
    local doc_files=(
        "docs/user-authentication.md"
        "README-UserAuth.md"
        "IMPLEMENTATION-SUMMARY.md"
    )
    
    for doc in "${doc_files[@]}"; do
        if [ -f "$doc" ]; then
            if [ -s "$doc" ]; then
                log_info "✓ $doc exists and is not empty"
            else
                log_warn "$doc exists but is empty"
            fi
        else
            log_error "✗ $doc not found"
        fi
    done
}

# Check script permissions
check_permissions() {
    log_info "Checking script permissions..."
    
    local scripts=(
        "scripts/test-user-auth.sh"
        "scripts/build-test.sh"
        "scripts/syntax-check.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [ -f "$script" ]; then
            if [ -x "$script" ]; then
                log_info "✓ $script is executable"
            else
                log_warn "$script exists but is not executable"
                chmod +x "$script"
                log_info "✓ Made $script executable"
            fi
        fi
    done
}

# Check integration points
check_integration() {
    log_info "Checking integration points..."
    
    # Check if headlamp.go has userauth imports
    if grep -q "userauth" "backend/cmd/headlamp.go"; then
        log_info "✓ headlamp.go has userauth integration"
    else
        log_error "✗ headlamp.go missing userauth integration"
        return 1
    fi
    
    # Check if config.go has userauth flags
    if grep -q "enable-user-auth" "backend/pkg/config/config.go"; then
        log_info "✓ config.go has userauth flags"
    else
        log_error "✗ config.go missing userauth flags"
        return 1
    fi
    
    # Check if server.go passes userauth config
    if grep -q "enableUserAuth" "backend/cmd/server.go"; then
        log_info "✓ server.go has userauth config"
    else
        log_error "✗ server.go missing userauth config"
        return 1
    fi
}

# Main validation
main() {
    log_info "Starting Headlamp user authentication syntax validation..."
    echo
    
    local failed_checks=0
    
    check_files || ((failed_checks++))
    echo
    
    check_go_syntax || ((failed_checks++))
    echo
    
    check_imports || ((failed_checks++))
    echo
    
    check_config_files || ((failed_checks++))
    echo
    
    check_documentation || ((failed_checks++))
    echo
    
    check_permissions || ((failed_checks++))
    echo
    
    check_integration || ((failed_checks++))
    echo
    
    if [ $failed_checks -eq 0 ]; then
        log_info "🎉 All syntax validation checks passed!"
        echo
        log_info "The user authentication system appears to be correctly implemented."
        log_info "You can now proceed with:"
        log_info "1. Installing Go and running: make -f Makefile.userauth build-userauth"
        log_info "2. Setting up test environment: make -f Makefile.userauth dev-setup"
        log_info "3. Running tests: make -f Makefile.userauth test-userauth"
        echo
        return 0
    else
        log_error "❌ $failed_checks validation check(s) failed"
        log_error "Please fix the issues above before proceeding."
        echo
        return 1
    fi
}

# Run main function
main "$@"
