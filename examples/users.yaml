# Headlamp User Authentication Configuration
# This file defines users and their associated Kubernetes ServiceAccounts
# 
# Each user entry contains:
# - username: The login username
# - passwordHash: bcrypt hash of the password
# - serviceAccount: The Kubernetes ServiceAccount to bind to this user
# - namespace: The namespace where the ServiceAccount exists
# - enabled: Whether the user account is active
# - description: Optional description of the user
# - createdAt: When the user was created
# - lastLoginAt: When the user last logged in (updated automatically)

users:
  # Admin user with cluster-admin privileges
  - username: admin
    passwordHash: $2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL9B8L3Sm  # password: admin123
    serviceAccount: headlamp-admin
    namespace: kube-system
    enabled: true
    description: "System administrator with full cluster access"
    createdAt: 2025-01-01T00:00:00Z

  # Developer user with limited privileges
  - username: developer
    passwordHash: $2a$12$9y8Nd2j3kL4mN5oP6qR7sT8uV9wX0yZ1aB2cD3eF4gH5iJ6kL7mN8o  # password: dev123
    serviceAccount: headlamp-developer
    namespace: default
    enabled: true
    description: "Developer with read-write access to development resources"
    createdAt: 2025-01-01T00:00:00Z

  # Read-only user
  - username: viewer
    passwordHash: $2a$12$8x7Mc1i2jK3lM4nO5pQ6rS7tU8vW9xY0zA1bC2dE3fG4hI5jK6lM7n  # password: view123
    serviceAccount: headlamp-viewer
    namespace: default
    enabled: true
    description: "Read-only access to cluster resources"
    createdAt: 2025-01-01T00:00:00Z

  # Disabled user example
  - username: olduser
    passwordHash: $2a$12$7w6Lb0h1iJ2kL3mN4oP5qR6sT7uV8wX9yZ0aB1cD2eF3gH4iJ5kL6m
    serviceAccount: headlamp-olduser
    namespace: default
    enabled: false
    description: "Disabled user account"
    createdAt: 2024-12-01T00:00:00Z
    lastLoginAt: 2024-12-15T10:30:00Z

# Note: Password hashes above are examples. In production:
# 1. Use the userpass-config-gen tool to generate secure password hashes
# 2. Ensure this file has restricted permissions (0600)
# 3. Store it in a secure location
# 4. Regularly rotate passwords
# 5. Monitor access logs

# Example ServiceAccount and RBAC setup for the users above:
# 
# apiVersion: v1
# kind: ServiceAccount
# metadata:
#   name: headlamp-admin
#   namespace: kube-system
# ---
# apiVersion: rbac.authorization.k8s.io/v1
# kind: ClusterRoleBinding
# metadata:
#   name: headlamp-admin
# roleRef:
#   apiGroup: rbac.authorization.k8s.io
#   kind: ClusterRole
#   name: cluster-admin
# subjects:
# - kind: ServiceAccount
#   name: headlamp-admin
#   namespace: kube-system
# ---
# apiVersion: v1
# kind: ServiceAccount
# metadata:
#   name: headlamp-developer
#   namespace: default
# ---
# apiVersion: rbac.authorization.k8s.io/v1
# kind: ClusterRoleBinding
# metadata:
#   name: headlamp-developer
# roleRef:
#   apiGroup: rbac.authorization.k8s.io
#   kind: ClusterRole
#   name: edit
# subjects:
# - kind: ServiceAccount
#   name: headlamp-developer
#   namespace: default
# ---
# apiVersion: v1
# kind: ServiceAccount
# metadata:
#   name: headlamp-viewer
#   namespace: default
# ---
# apiVersion: rbac.authorization.k8s.io/v1
# kind: ClusterRoleBinding
# metadata:
#   name: headlamp-viewer
# roleRef:
#   apiGroup: rbac.authorization.k8s.io
#   kind: ClusterRole
#   name: view
# subjects:
# - kind: ServiceAccount
#   name: headlamp-viewer
#   namespace: default
