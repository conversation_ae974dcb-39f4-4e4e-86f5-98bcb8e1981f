/*
Copyright 2025 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package main

import (
	"os"
	"strings"
	"time"

	"github.com/kubernetes-sigs/headlamp/backend/pkg/cache"
	"github.com/kubernetes-sigs/headlamp/backend/pkg/config"
	"github.com/kubernetes-sigs/headlamp/backend/pkg/kubeconfig"
	"github.com/kubernetes-sigs/headlamp/backend/pkg/logger"
	"github.com/kubernetes-sigs/headlamp/backend/pkg/plugins"
)

func main() {
	if len(os.Args) == 2 && os.Args[1] == "list-plugins" {
		runListPlugins()

		return
	}

	conf, err := config.Parse(os.Args)
	if err != nil {
		logger.Log(logger.LevelError, nil, err, "fetching config:%v")
		os.Exit(1)
	}

	cache := cache.New[interface{}]()
	kubeConfigStore := kubeconfig.NewContextStore()
	multiplexer := NewMultiplexer(kubeConfigStore)

	// Parse user auth token expiration
	var userAuthTokenExpiration time.Duration
	if conf.UserAuthTokenExpiration != "" {
		var err error
		userAuthTokenExpiration, err = time.ParseDuration(conf.UserAuthTokenExpiration)
		if err != nil {
			logger.Log(logger.LevelError, nil, err, "invalid user-auth-token-expiration format")
			userAuthTokenExpiration = 24 * time.Hour // default to 24 hours
		}
	} else {
		userAuthTokenExpiration = 24 * time.Hour // default to 24 hours
	}

	StartHeadlampServer(&HeadlampConfig{
		useInCluster:              conf.InCluster,
		kubeConfigPath:            conf.KubeConfigPath,
		skippedKubeContexts:       conf.SkippedKubeContexts,
		listenAddr:                conf.ListenAddr,
		port:                      conf.Port,
		devMode:                   conf.DevMode,
		staticDir:                 conf.StaticDir,
		insecure:                  conf.InsecureSsl,
		pluginDir:                 conf.PluginsDir,
		oidcClientID:              conf.OidcClientID,
		oidcValidatorClientID:     conf.OidcValidatorClientID,
		oidcClientSecret:          conf.OidcClientSecret,
		oidcIdpIssuerURL:          conf.OidcIdpIssuerURL,
		oidcValidatorIdpIssuerURL: conf.OidcValidatorIdpIssuerURL,
		oidcScopes:                strings.Split(conf.OidcScopes, ","),
		oidcUseAccessToken:        conf.OidcUseAccessToken,
		baseURL:                   conf.BaseURL,
		proxyURLs:                 strings.Split(conf.ProxyURLs, ","),
		enableHelm:                conf.EnableHelm,
		enableDynamicClusters:     conf.EnableDynamicClusters,
		watchPluginsChanges:       conf.WatchPluginsChanges,
		enableUserAuth:            conf.EnableUserAuth,
		userAuthConfigFile:        conf.UserAuthConfigFile,
		userAuthJWTSecret:         conf.UserAuthJWTSecret,
		userAuthTokenExpiration:   userAuthTokenExpiration,
		cache:                     cache,
		kubeConfigStore:           kubeConfigStore,
		multiplexer:               multiplexer,
		telemetryConfig: config.Config{
			ServiceName:        conf.ServiceName,
			ServiceVersion:     conf.ServiceVersion,
			TracingEnabled:     conf.TracingEnabled,
			MetricsEnabled:     conf.MetricsEnabled,
			JaegerEndpoint:     conf.JaegerEndpoint,
			OTLPEndpoint:       conf.OTLPEndpoint,
			UseOTLPHTTP:        conf.UseOTLPHTTP,
			StdoutTraceEnabled: conf.StdoutTraceEnabled,
			SamplingRate:       conf.SamplingRate,
		},
	})
}

func runListPlugins() {
	conf, err := config.Parse(os.Args[2:])
	if err != nil {
		logger.Log(logger.LevelError, nil, err, "fetching config:%v")
		os.Exit(1)
	}

	if err := plugins.ListPlugins(conf.StaticDir, conf.PluginsDir); err != nil {
		logger.Log(logger.LevelError, nil, err, "listing plugins")
	}
}
