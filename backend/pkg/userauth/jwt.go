/*
Copyright 2025 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package userauth

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"
)

var (
	// ErrInvalidToken is returned when token is invalid
	ErrInvalidToken = errors.New("invalid token")
	// ErrTokenExpired is returned when token is expired
	ErrTokenExpired = errors.New("token expired")
	// ErrInvalidSignature is returned when token signature is invalid
	ErrInvalidSignature = errors.New("invalid token signature")
)

// JWTManager handles JWT token operations
type JWTManager struct {
	secretKey  []byte
	expiration time.Duration
}

// NewJWTManager creates a new JWT manager
func NewJWTManager(secretKey string, expiration time.Duration) *JWTManager {
	return &JWTManager{
		secretKey:  []byte(secretKey),
		expiration: expiration,
	}
}

// GenerateToken generates a JWT token for the user
func (j *JWTManager) GenerateToken(user *User) (string, time.Time, error) {
	now := time.Now()
	expiresAt := now.Add(j.expiration)

	claims := JWTClaims{
		Username:       user.Username,
		ServiceAccount: user.ServiceAccount,
		Namespace:      user.Namespace,
		IssuedAt:       now.Unix(),
		ExpiresAt:      expiresAt.Unix(),
	}

	token, err := j.createToken(claims)
	if err != nil {
		return "", time.Time{}, fmt.Errorf("failed to create token: %w", err)
	}

	return token, expiresAt, nil
}

// ValidateToken validates and parses a JWT token
func (j *JWTManager) ValidateToken(tokenString string) (*JWTClaims, error) {
	parts := strings.Split(tokenString, ".")
	if len(parts) != 3 {
		return nil, ErrInvalidToken
	}

	// Decode header
	headerBytes, err := base64.RawURLEncoding.DecodeString(parts[0])
	if err != nil {
		return nil, ErrInvalidToken
	}

	var header map[string]interface{}
	if err := json.Unmarshal(headerBytes, &header); err != nil {
		return nil, ErrInvalidToken
	}

	// Check algorithm
	if alg, ok := header["alg"].(string); !ok || alg != "HS256" {
		return nil, ErrInvalidToken
	}

	// Decode payload
	payloadBytes, err := base64.RawURLEncoding.DecodeString(parts[1])
	if err != nil {
		return nil, ErrInvalidToken
	}

	var claims JWTClaims
	if err := json.Unmarshal(payloadBytes, &claims); err != nil {
		return nil, ErrInvalidToken
	}

	// Check expiration
	if time.Now().Unix() > claims.ExpiresAt {
		return nil, ErrTokenExpired
	}

	// Verify signature
	expectedSignature := j.sign(parts[0] + "." + parts[1])
	if !SecureCompare(parts[2], expectedSignature) {
		return nil, ErrInvalidSignature
	}

	return &claims, nil
}

// RefreshToken generates a new token with extended expiration
func (j *JWTManager) RefreshToken(tokenString string) (string, time.Time, error) {
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		// Allow refresh of expired tokens within a grace period
		if !errors.Is(err, ErrTokenExpired) {
			return "", time.Time{}, err
		}

		// Check if token expired within grace period (e.g., 1 hour)
		gracePeriod := time.Hour
		if time.Now().Unix()-claims.ExpiresAt > int64(gracePeriod.Seconds()) {
			return "", time.Time{}, ErrTokenExpired
		}
	}

	// Create new token with same claims but new expiration
	now := time.Now()
	expiresAt := now.Add(j.expiration)

	newClaims := JWTClaims{
		Username:       claims.Username,
		ServiceAccount: claims.ServiceAccount,
		Namespace:      claims.Namespace,
		IssuedAt:       now.Unix(),
		ExpiresAt:      expiresAt.Unix(),
	}

	token, err := j.createToken(newClaims)
	if err != nil {
		return "", time.Time{}, fmt.Errorf("failed to create refreshed token: %w", err)
	}

	return token, expiresAt, nil
}

// createToken creates a JWT token with the given claims
func (j *JWTManager) createToken(claims JWTClaims) (string, error) {
	// Create header
	header := map[string]interface{}{
		"alg": "HS256",
		"typ": "JWT",
	}

	headerBytes, err := json.Marshal(header)
	if err != nil {
		return "", err
	}

	// Create payload
	payloadBytes, err := json.Marshal(claims)
	if err != nil {
		return "", err
	}

	// Encode header and payload
	headerEncoded := base64.RawURLEncoding.EncodeToString(headerBytes)
	payloadEncoded := base64.RawURLEncoding.EncodeToString(payloadBytes)

	// Create signature
	message := headerEncoded + "." + payloadEncoded
	signature := j.sign(message)

	// Combine all parts
	token := message + "." + signature

	return token, nil
}

// sign creates HMAC-SHA256 signature for the message
func (j *JWTManager) sign(message string) string {
	h := hmac.New(sha256.New, j.secretKey)
	h.Write([]byte(message))
	return base64.RawURLEncoding.EncodeToString(h.Sum(nil))
}

// GetTokenExpiration returns the token expiration duration
func (j *JWTManager) GetTokenExpiration() time.Duration {
	return j.expiration
}

// SetTokenExpiration sets the token expiration duration
func (j *JWTManager) SetTokenExpiration(expiration time.Duration) {
	j.expiration = expiration
}
