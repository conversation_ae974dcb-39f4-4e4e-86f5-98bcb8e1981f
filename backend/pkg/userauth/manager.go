/*
Copyright 2025 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package userauth

import (
	"context"
	"errors"
	"fmt"
	"io/ioutil"
	"os"
	"sync"
	"time"

	"gopkg.in/yaml.v2"
	"k8s.io/client-go/rest"
)

var (
	// ErrUserNotFound is returned when user is not found
	ErrUserNotFound = errors.New("user not found")
	// ErrUserDisabled is returned when user is disabled
	ErrUserDisabled = errors.New("user is disabled")
	// ErrAuthenticationDisabled is returned when authentication is disabled
	ErrAuthenticationDisabled = errors.New("user authentication is disabled")
)

// UserManager manages user authentication and authorization
type UserManager struct {
	config                      *AuthConfig
	users                       map[string]*User
	passwordHasher              PasswordHasher
	jwtManager                  *JWTManager
	serviceAccountTokenManager  *ServiceAccountTokenManager
	mutex                       sync.RWMutex
	configFile                  string
	lastConfigModTime           time.Time
}

// NewUserManager creates a new user manager
func NewUserManager(config *AuthConfig, kubeConfig *rest.Config) (*UserManager, error) {
	if config == nil {
		config = DefaultAuthConfig()
	}

	// Initialize password hasher
	passwordHasher := NewBcryptHasher(DefaultBcryptCost)

	// Initialize JWT manager
	if config.JWTSecret == "" {
		secret, err := GenerateSecretKey()
		if err != nil {
			return nil, fmt.Errorf("failed to generate JWT secret: %w", err)
		}
		config.JWTSecret = secret
	}

	jwtManager := NewJWTManager(config.JWTSecret, config.TokenExpiration)

	// Initialize service account token manager
	saTokenManager, err := NewServiceAccountTokenManager(kubeConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create service account token manager: %w", err)
	}

	manager := &UserManager{
		config:                     config,
		users:                      make(map[string]*User),
		passwordHasher:             passwordHasher,
		jwtManager:                 jwtManager,
		serviceAccountTokenManager: saTokenManager,
		configFile:                 config.ConfigFile,
	}

	// Load users from config
	if err := manager.loadUsers(); err != nil {
		return nil, fmt.Errorf("failed to load users: %w", err)
	}

	return manager, nil
}

// IsEnabled returns whether user authentication is enabled
func (m *UserManager) IsEnabled() bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.config.Enabled
}

// Authenticate authenticates a user with username and password
func (m *UserManager) Authenticate(ctx context.Context, username, password string) (*LoginResponse, error) {
	if !m.IsEnabled() {
		return nil, ErrAuthenticationDisabled
	}

	m.mutex.RLock()
	user, exists := m.users[username]
	m.mutex.RUnlock()

	if !exists {
		return nil, ErrUserNotFound
	}

	if !user.Enabled {
		return nil, ErrUserDisabled
	}

	// Verify password
	if err := m.passwordHasher.VerifyPassword(password, user.PasswordHash); err != nil {
		return nil, err
	}

	// Validate service account
	if err := m.serviceAccountTokenManager.ValidateServiceAccount(ctx, user.Namespace, user.ServiceAccount); err != nil {
		return nil, fmt.Errorf("service account validation failed: %w", err)
	}

	// Generate JWT token
	token, expiresAt, err := m.jwtManager.GenerateToken(user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}

	// Update last login time
	now := time.Now()
	m.mutex.Lock()
	user.LastLoginAt = &now
	m.mutex.Unlock()

	return &LoginResponse{
		Token:     token,
		ExpiresAt: expiresAt,
		User: UserInfo{
			Username:       user.Username,
			ServiceAccount: user.ServiceAccount,
			Namespace:      user.Namespace,
			LastLoginAt:    user.LastLoginAt,
		},
	}, nil
}

// ValidateToken validates a JWT token and returns the claims
func (m *UserManager) ValidateToken(tokenString string) (*JWTClaims, error) {
	if !m.IsEnabled() {
		return nil, ErrAuthenticationDisabled
	}

	return m.jwtManager.ValidateToken(tokenString)
}

// GetServiceAccountToken gets the service account token for a user
func (m *UserManager) GetServiceAccountToken(ctx context.Context, username string) (string, error) {
	if !m.IsEnabled() {
		return "", ErrAuthenticationDisabled
	}

	m.mutex.RLock()
	user, exists := m.users[username]
	m.mutex.RUnlock()

	if !exists {
		return "", ErrUserNotFound
	}

	if !user.Enabled {
		return "", ErrUserDisabled
	}

	return m.serviceAccountTokenManager.GetServiceAccountToken(ctx, user.Namespace, user.ServiceAccount)
}

// RefreshToken refreshes a JWT token
func (m *UserManager) RefreshToken(tokenString string) (string, time.Time, error) {
	if !m.IsEnabled() {
		return "", time.Time{}, ErrAuthenticationDisabled
	}

	return m.jwtManager.RefreshToken(tokenString)
}

// loadUsers loads users from configuration file or config
func (m *UserManager) loadUsers() error {
	// First try to load from file
	if m.configFile != "" && fileExists(m.configFile) {
		return m.loadUsersFromFile()
	}

	// Fall back to config
	return m.loadUsersFromConfig()
}

// loadUsersFromFile loads users from YAML configuration file
func (m *UserManager) loadUsersFromFile() error {
	data, err := ioutil.ReadFile(m.configFile)
	if err != nil {
		return fmt.Errorf("failed to read config file %s: %w", m.configFile, err)
	}

	var userConfig UserConfig
	if err := yaml.Unmarshal(data, &userConfig); err != nil {
		return fmt.Errorf("failed to parse config file %s: %w", m.configFile, err)
	}

	// Update last modification time
	if stat, err := os.Stat(m.configFile); err == nil {
		m.lastConfigModTime = stat.ModTime()
	}

	return m.loadUsersFromUserConfig(&userConfig)
}

// loadUsersFromConfig loads users from the AuthConfig
func (m *UserManager) loadUsersFromConfig() error {
	userConfig := &UserConfig{
		Users: m.config.Users,
	}
	return m.loadUsersFromUserConfig(userConfig)
}

// loadUsersFromUserConfig loads users from UserConfig struct
func (m *UserManager) loadUsersFromUserConfig(userConfig *UserConfig) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// Clear existing users
	m.users = make(map[string]*User)

	// Load users
	for i := range userConfig.Users {
		user := &userConfig.Users[i]
		
		// Validate user configuration
		if err := m.validateUser(user); err != nil {
			return fmt.Errorf("invalid user configuration for %s: %w", user.Username, err)
		}

		m.users[user.Username] = user
	}

	return nil
}

// validateUser validates user configuration
func (m *UserManager) validateUser(user *User) error {
	if user.Username == "" {
		return errors.New("username cannot be empty")
	}

	if user.PasswordHash == "" {
		return errors.New("password hash cannot be empty")
	}

	if err := ParsePasswordHash(user.PasswordHash); err != nil {
		return fmt.Errorf("invalid password hash: %w", err)
	}

	if user.ServiceAccount == "" {
		return errors.New("service account cannot be empty")
	}

	if user.Namespace == "" {
		user.Namespace = "default"
	}

	return nil
}

// ReloadConfig reloads the configuration if the file has changed
func (m *UserManager) ReloadConfig() error {
	if m.configFile == "" || !fileExists(m.configFile) {
		return nil
	}

	stat, err := os.Stat(m.configFile)
	if err != nil {
		return err
	}

	// Check if file has been modified
	if !stat.ModTime().After(m.lastConfigModTime) {
		return nil
	}

	return m.loadUsersFromFile()
}

// GetUsers returns a list of all users (without sensitive information)
func (m *UserManager) GetUsers() []UserInfo {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	users := make([]UserInfo, 0, len(m.users))
	for _, user := range m.users {
		if user.Enabled {
			users = append(users, UserInfo{
				Username:       user.Username,
				ServiceAccount: user.ServiceAccount,
				Namespace:      user.Namespace,
				LastLoginAt:    user.LastLoginAt,
			})
		}
	}

	return users
}

// fileExists checks if a file exists
func fileExists(filename string) bool {
	_, err := os.Stat(filename)
	return !os.IsNotExist(err)
}
