/*
Copyright 2025 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package userauth

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/kubernetes-sigs/headlamp/backend/pkg/logger"
)

// AuthHandlers contains HTTP handlers for authentication
type AuthHandlers struct {
	userManager *UserManager
}

// NewAuthHandlers creates new authentication handlers
func NewAuthHandlers(userManager *UserManager) *AuthHandlers {
	return &AuthHandlers{
		userManager: userManager,
	}
}

// LoginHandler handles user login requests
func (h *AuthHandlers) LoginHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Check if authentication is enabled
	if !h.userManager.IsEnabled() {
		http.Error(w, "User authentication is disabled", http.StatusServiceUnavailable)
		return
	}

	// Parse request body
	var loginReq LoginRequest
	if err := json.NewDecoder(r.Body).Decode(&loginReq); err != nil {
		logger.Log(logger.LevelError, nil, err, "Failed to decode login request")
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validate input
	if loginReq.Username == "" || loginReq.Password == "" {
		http.Error(w, "Username and password are required", http.StatusBadRequest)
		return
	}

	// Authenticate user
	loginResp, err := h.userManager.Authenticate(r.Context(), loginReq.Username, loginReq.Password)
	if err != nil {
		logger.Log(logger.LevelError, map[string]string{"username": loginReq.Username}, err, "Authentication failed")
		
		// Return generic error message for security
		if errors.Is(err, ErrUserNotFound) || errors.Is(err, ErrPasswordMismatch) {
			http.Error(w, "Invalid username or password", http.StatusUnauthorized)
		} else if errors.Is(err, ErrUserDisabled) {
			http.Error(w, "User account is disabled", http.StatusForbidden)
		} else {
			http.Error(w, "Authentication failed", http.StatusInternalServerError)
		}
		return
	}

	// Set response headers
	w.Header().Set("Content-Type", "application/json")
	
	// Set secure cookie with the token (optional, for browser-based clients)
	http.SetCookie(w, &http.Cookie{
		Name:     "headlamp-auth-token",
		Value:    loginResp.Token,
		Expires:  loginResp.ExpiresAt,
		HttpOnly: true,
		Secure:   r.TLS != nil, // Only set secure flag if using HTTPS
		SameSite: http.SameSiteStrictMode,
		Path:     "/",
	})

	// Return response
	if err := json.NewEncoder(w).Encode(loginResp); err != nil {
		logger.Log(logger.LevelError, nil, err, "Failed to encode login response")
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	logger.Log(logger.LevelInfo, map[string]string{"username": loginReq.Username}, nil, "User logged in successfully")
}

// RefreshHandler handles token refresh requests
func (h *AuthHandlers) RefreshHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Check if authentication is enabled
	if !h.userManager.IsEnabled() {
		http.Error(w, "User authentication is disabled", http.StatusServiceUnavailable)
		return
	}

	// Get token from header or cookie
	token := h.extractToken(r)
	if token == "" {
		http.Error(w, "No token provided", http.StatusUnauthorized)
		return
	}

	// Refresh token
	newToken, expiresAt, err := h.userManager.RefreshToken(token)
	if err != nil {
		logger.Log(logger.LevelError, nil, err, "Token refresh failed")
		
		if errors.Is(err, ErrTokenExpired) {
			http.Error(w, "Token expired", http.StatusUnauthorized)
		} else if errors.Is(err, ErrInvalidToken) {
			http.Error(w, "Invalid token", http.StatusUnauthorized)
		} else {
			http.Error(w, "Token refresh failed", http.StatusInternalServerError)
		}
		return
	}

	// Create response
	response := map[string]interface{}{
		"token":     newToken,
		"expiresAt": expiresAt,
	}

	// Set response headers
	w.Header().Set("Content-Type", "application/json")
	
	// Update cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "headlamp-auth-token",
		Value:    newToken,
		Expires:  expiresAt,
		HttpOnly: true,
		Secure:   r.TLS != nil,
		SameSite: http.SameSiteStrictMode,
		Path:     "/",
	})

	// Return response
	if err := json.NewEncoder(w).Encode(response); err != nil {
		logger.Log(logger.LevelError, nil, err, "Failed to encode refresh response")
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
}

// LogoutHandler handles user logout requests
func (h *AuthHandlers) LogoutHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Clear the auth cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "headlamp-auth-token",
		Value:    "",
		Expires:  time.Unix(0, 0),
		HttpOnly: true,
		Secure:   r.TLS != nil,
		SameSite: http.SameSiteStrictMode,
		Path:     "/",
	})

	// Return success response
	w.Header().Set("Content-Type", "application/json")
	response := map[string]string{"message": "Logged out successfully"}
	json.NewEncoder(w).Encode(response)
}

// StatusHandler returns the authentication status
func (h *AuthHandlers) StatusHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	response := map[string]interface{}{
		"enabled": h.userManager.IsEnabled(),
	}

	// If authentication is enabled and user is authenticated, return user info
	if h.userManager.IsEnabled() {
		token := h.extractToken(r)
		if token != "" {
			if claims, err := h.userManager.ValidateToken(token); err == nil {
				response["authenticated"] = true
				response["user"] = map[string]interface{}{
					"username":       claims.Username,
					"serviceAccount": claims.ServiceAccount,
					"namespace":      claims.Namespace,
				}
			} else {
				response["authenticated"] = false
			}
		} else {
			response["authenticated"] = false
		}
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// extractToken extracts the JWT token from the request
func (h *AuthHandlers) extractToken(r *http.Request) string {
	// First try Authorization header
	authHeader := r.Header.Get("Authorization")
	if authHeader != "" {
		// Check for Bearer token
		if strings.HasPrefix(authHeader, "Bearer ") {
			return strings.TrimPrefix(authHeader, "Bearer ")
		}
	}

	// Then try cookie
	if cookie, err := r.Cookie("headlamp-auth-token"); err == nil {
		return cookie.Value
	}

	return ""
}

// AuthMiddleware is a middleware that validates JWT tokens
func (h *AuthHandlers) AuthMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Skip authentication if disabled
		if !h.userManager.IsEnabled() {
			next.ServeHTTP(w, r)
			return
		}

		// Extract token
		token := h.extractToken(r)
		if token == "" {
			http.Error(w, "Authentication required", http.StatusUnauthorized)
			return
		}

		// Validate token
		claims, err := h.userManager.ValidateToken(token)
		if err != nil {
			logger.Log(logger.LevelError, nil, err, "Token validation failed")
			
			if errors.Is(err, ErrTokenExpired) {
				http.Error(w, "Token expired", http.StatusUnauthorized)
			} else {
				http.Error(w, "Invalid token", http.StatusUnauthorized)
			}
			return
		}

		// Get service account token
		saToken, err := h.userManager.GetServiceAccountToken(r.Context(), claims.Username)
		if err != nil {
			logger.Log(logger.LevelError, map[string]string{"username": claims.Username}, err, "Failed to get service account token")
			http.Error(w, "Failed to get service account token", http.StatusInternalServerError)
			return
		}

		// Add service account token to request headers for Kubernetes API calls
		r.Header.Set("Authorization", "Bearer "+saToken)
		
		// Add user context to request
		r.Header.Set("X-Headlamp-User", claims.Username)
		r.Header.Set("X-Headlamp-ServiceAccount", claims.ServiceAccount)
		r.Header.Set("X-Headlamp-Namespace", claims.Namespace)

		next.ServeHTTP(w, r)
	})
}
