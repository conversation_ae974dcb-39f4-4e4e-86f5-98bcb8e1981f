/*
Copyright 2025 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package userauth

import (
	"strings"
	"testing"
)

func TestBcryptHasher_HashPassword(t *testing.T) {
	hasher := NewBcryptHasher(DefaultBcryptCost)

	tests := []struct {
		name        string
		password    string
		expectError bool
	}{
		{
			name:        "valid password",
			password:    "validpassword123",
			expectError: false,
		},
		{
			name:        "short password",
			password:    "short",
			expectError: true,
		},
		{
			name:        "empty password",
			password:    "",
			expectError: true,
		},
		{
			name:        "long password",
			password:    strings.Repeat("a", 100),
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			hash, err := hasher.HashPassword(tt.password)
			
			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if hash == "" {
				t.Errorf("expected non-empty hash")
			}

			// Verify the hash starts with bcrypt prefix
			if !strings.HasPrefix(hash, "$2a$") && !strings.HasPrefix(hash, "$2b$") {
				t.Errorf("hash doesn't have bcrypt prefix: %s", hash)
			}
		})
	}
}

func TestBcryptHasher_VerifyPassword(t *testing.T) {
	hasher := NewBcryptHasher(DefaultBcryptCost)
	password := "testpassword123"
	
	hash, err := hasher.HashPassword(password)
	if err != nil {
		t.Fatalf("failed to hash password: %v", err)
	}

	tests := []struct {
		name        string
		password    string
		hash        string
		expectError bool
	}{
		{
			name:        "correct password",
			password:    password,
			hash:        hash,
			expectError: false,
		},
		{
			name:        "wrong password",
			password:    "wrongpassword",
			hash:        hash,
			expectError: true,
		},
		{
			name:        "empty password",
			password:    "",
			hash:        hash,
			expectError: true,
		},
		{
			name:        "empty hash",
			password:    password,
			hash:        "",
			expectError: true,
		},
		{
			name:        "invalid hash",
			password:    password,
			hash:        "invalid-hash",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := hasher.VerifyPassword(tt.password, tt.hash)
			
			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
			}
		})
	}
}

func TestBcryptHasher_GenerateRandomPassword(t *testing.T) {
	hasher := NewBcryptHasher(DefaultBcryptCost)

	tests := []struct {
		name   string
		length int
	}{
		{
			name:   "minimum length",
			length: MinPasswordLength,
		},
		{
			name:   "short length (should use minimum)",
			length: 4,
		},
		{
			name:   "medium length",
			length: 16,
		},
		{
			name:   "long length",
			length: 32,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			password, err := hasher.GenerateRandomPassword(tt.length)
			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			expectedLength := tt.length
			if expectedLength < MinPasswordLength {
				expectedLength = MinPasswordLength
			}

			if len(password) != expectedLength {
				t.Errorf("expected password length %d, got %d", expectedLength, len(password))
			}

			// Verify password can be hashed
			_, err = hasher.HashPassword(password)
			if err != nil {
				t.Errorf("generated password cannot be hashed: %v", err)
			}
		})
	}
}

func TestValidatePassword(t *testing.T) {
	tests := []struct {
		name        string
		password    string
		expectError bool
	}{
		{
			name:        "valid password",
			password:    "validpassword123",
			expectError: false,
		},
		{
			name:        "short password",
			password:    "short",
			expectError: true,
		},
		{
			name:        "empty password",
			password:    "",
			expectError: true,
		},
		{
			name:        "minimum length password",
			password:    "12345678",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidatePassword(tt.password)
			
			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
			}
		})
	}
}

func TestParsePasswordHash(t *testing.T) {
	hasher := NewBcryptHasher(DefaultBcryptCost)
	validHash, _ := hasher.HashPassword("testpassword")

	tests := []struct {
		name        string
		hash        string
		expectError bool
	}{
		{
			name:        "valid bcrypt hash",
			hash:        validHash,
			expectError: false,
		},
		{
			name:        "empty hash",
			hash:        "",
			expectError: true,
		},
		{
			name:        "invalid hash format",
			hash:        "invalid-hash",
			expectError: true,
		},
		{
			name:        "md5 hash (not supported)",
			hash:        "5d41402abc4b2a76b9719d911017c592",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ParsePasswordHash(tt.hash)
			
			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
			}
		})
	}
}

func TestSecureCompare(t *testing.T) {
	tests := []struct {
		name     string
		a        string
		b        string
		expected bool
	}{
		{
			name:     "equal strings",
			a:        "hello",
			b:        "hello",
			expected: true,
		},
		{
			name:     "different strings",
			a:        "hello",
			b:        "world",
			expected: false,
		},
		{
			name:     "empty strings",
			a:        "",
			b:        "",
			expected: true,
		},
		{
			name:     "one empty string",
			a:        "hello",
			b:        "",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SecureCompare(tt.a, tt.b)
			if result != tt.expected {
				t.Errorf("expected %v, got %v", tt.expected, result)
			}
		})
	}
}

func TestGenerateSecretKey(t *testing.T) {
	key1, err := GenerateSecretKey()
	if err != nil {
		t.Errorf("unexpected error: %v", err)
	}

	if key1 == "" {
		t.Errorf("expected non-empty key")
	}

	// Generate another key to ensure they're different
	key2, err := GenerateSecretKey()
	if err != nil {
		t.Errorf("unexpected error: %v", err)
	}

	if key1 == key2 {
		t.Errorf("expected different keys, got same: %s", key1)
	}
}
