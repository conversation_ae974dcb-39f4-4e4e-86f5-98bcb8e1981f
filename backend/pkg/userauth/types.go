/*
Copyright 2025 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package userauth

import (
	"time"
)

// User represents a user in the system
type User struct {
	Username         string    `json:"username" yaml:"username"`
	PasswordHash     string    `json:"passwordHash" yaml:"passwordHash"`
	ServiceAccount   string    `json:"serviceAccount" yaml:"serviceAccount"`
	Namespace        string    `json:"namespace" yaml:"namespace"`
	Enabled          bool      `json:"enabled" yaml:"enabled"`
	CreatedAt        time.Time `json:"createdAt" yaml:"createdAt"`
	LastLoginAt      *time.Time `json:"lastLoginAt,omitempty" yaml:"lastLoginAt,omitempty"`
	Description      string    `json:"description,omitempty" yaml:"description,omitempty"`
}

// UserConfig represents the configuration for user authentication
type UserConfig struct {
	Users []User `json:"users" yaml:"users"`
}

// LoginRequest represents a login request
type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// LoginResponse represents a login response
type LoginResponse struct {
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expiresAt"`
	User      UserInfo  `json:"user"`
}

// UserInfo represents user information returned to the client
type UserInfo struct {
	Username       string     `json:"username"`
	ServiceAccount string     `json:"serviceAccount"`
	Namespace      string     `json:"namespace"`
	LastLoginAt    *time.Time `json:"lastLoginAt,omitempty"`
}

// JWTClaims represents the JWT token claims
type JWTClaims struct {
	Username       string `json:"username"`
	ServiceAccount string `json:"serviceAccount"`
	Namespace      string `json:"namespace"`
	IssuedAt       int64  `json:"iat"`
	ExpiresAt      int64  `json:"exp"`
}

// ServiceAccountToken represents a cached service account token
type ServiceAccountToken struct {
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expiresAt"`
	Namespace string    `json:"namespace"`
	Name      string    `json:"name"`
}

// AuthConfig represents the authentication configuration
type AuthConfig struct {
	Enabled         bool          `json:"enabled" yaml:"enabled"`
	JWTSecret       string        `json:"jwtSecret" yaml:"jwtSecret"`
	TokenExpiration time.Duration `json:"tokenExpiration" yaml:"tokenExpiration"`
	ConfigFile      string        `json:"configFile" yaml:"configFile"`
	Users           []User        `json:"users" yaml:"users"`
}

// DefaultAuthConfig returns the default authentication configuration
func DefaultAuthConfig() *AuthConfig {
	return &AuthConfig{
		Enabled:         false,
		TokenExpiration: 24 * time.Hour, // 24 hours default
		ConfigFile:      "/etc/headlamp/users.yaml",
		Users:           []User{},
	}
}
