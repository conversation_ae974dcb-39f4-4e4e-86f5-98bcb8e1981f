/*
Copyright 2025 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package userauth

import (
	"testing"
	"time"
)

func TestJWTManager_GenerateToken(t *testing.T) {
	secretKey := "test-secret-key"
	expiration := time.Hour
	manager := NewJWTManager(secretKey, expiration)

	user := &User{
		Username:       "testuser",
		ServiceAccount: "test-sa",
		Namespace:      "default",
	}

	token, expiresAt, err := manager.GenerateToken(user)
	if err != nil {
		t.Errorf("unexpected error: %v", err)
		return
	}

	if token == "" {
		t.Errorf("expected non-empty token")
	}

	if expiresAt.Before(time.Now()) {
		t.<PERSON><PERSON><PERSON>("token should not be expired immediately")
	}

	// Verify token can be validated
	claims, err := manager.ValidateToken(token)
	if err != nil {
		t.Errorf("failed to validate generated token: %v", err)
		return
	}

	if claims.Username != user.Username {
		t.Errorf("expected username %s, got %s", user.Username, claims.Username)
	}

	if claims.ServiceAccount != user.ServiceAccount {
		t.Errorf("expected service account %s, got %s", user.ServiceAccount, claims.ServiceAccount)
	}

	if claims.Namespace != user.Namespace {
		t.Errorf("expected namespace %s, got %s", user.Namespace, claims.Namespace)
	}
}

func TestJWTManager_ValidateToken(t *testing.T) {
	secretKey := "test-secret-key"
	expiration := time.Hour
	manager := NewJWTManager(secretKey, expiration)

	user := &User{
		Username:       "testuser",
		ServiceAccount: "test-sa",
		Namespace:      "default",
	}

	token, _, err := manager.GenerateToken(user)
	if err != nil {
		t.Fatalf("failed to generate token: %v", err)
	}

	tests := []struct {
		name        string
		token       string
		expectError bool
	}{
		{
			name:        "valid token",
			token:       token,
			expectError: false,
		},
		{
			name:        "empty token",
			token:       "",
			expectError: true,
		},
		{
			name:        "invalid token format",
			token:       "invalid.token",
			expectError: true,
		},
		{
			name:        "malformed token",
			token:       "header.payload.signature.extra",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			claims, err := manager.ValidateToken(tt.token)
			
			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if claims == nil {
				t.Errorf("expected non-nil claims")
			}
		})
	}
}

func TestJWTManager_ValidateToken_ExpiredToken(t *testing.T) {
	secretKey := "test-secret-key"
	expiration := time.Millisecond // Very short expiration
	manager := NewJWTManager(secretKey, expiration)

	user := &User{
		Username:       "testuser",
		ServiceAccount: "test-sa",
		Namespace:      "default",
	}

	token, _, err := manager.GenerateToken(user)
	if err != nil {
		t.Fatalf("failed to generate token: %v", err)
	}

	// Wait for token to expire
	time.Sleep(time.Millisecond * 10)

	_, err = manager.ValidateToken(token)
	if err == nil {
		t.Errorf("expected error for expired token")
		return
	}

	if err != ErrTokenExpired {
		t.Errorf("expected ErrTokenExpired, got %v", err)
	}
}

func TestJWTManager_ValidateToken_WrongSecret(t *testing.T) {
	secretKey1 := "secret-key-1"
	secretKey2 := "secret-key-2"
	expiration := time.Hour

	manager1 := NewJWTManager(secretKey1, expiration)
	manager2 := NewJWTManager(secretKey2, expiration)

	user := &User{
		Username:       "testuser",
		ServiceAccount: "test-sa",
		Namespace:      "default",
	}

	token, _, err := manager1.GenerateToken(user)
	if err != nil {
		t.Fatalf("failed to generate token: %v", err)
	}

	// Try to validate with different secret
	_, err = manager2.ValidateToken(token)
	if err == nil {
		t.Errorf("expected error for token with wrong secret")
		return
	}

	if err != ErrInvalidSignature {
		t.Errorf("expected ErrInvalidSignature, got %v", err)
	}
}

func TestJWTManager_RefreshToken(t *testing.T) {
	secretKey := "test-secret-key"
	expiration := time.Hour
	manager := NewJWTManager(secretKey, expiration)

	user := &User{
		Username:       "testuser",
		ServiceAccount: "test-sa",
		Namespace:      "default",
	}

	originalToken, originalExpiresAt, err := manager.GenerateToken(user)
	if err != nil {
		t.Fatalf("failed to generate token: %v", err)
	}

	// Wait a bit to ensure new token has different issued time
	time.Sleep(time.Millisecond * 10)

	newToken, newExpiresAt, err := manager.RefreshToken(originalToken)
	if err != nil {
		t.Errorf("unexpected error: %v", err)
		return
	}

	if newToken == originalToken {
		t.Errorf("expected different token after refresh")
	}

	if !newExpiresAt.After(originalExpiresAt) {
		t.Errorf("expected new expiration to be later than original")
	}

	// Verify new token is valid
	claims, err := manager.ValidateToken(newToken)
	if err != nil {
		t.Errorf("failed to validate refreshed token: %v", err)
		return
	}

	if claims.Username != user.Username {
		t.Errorf("expected username %s, got %s", user.Username, claims.Username)
	}
}

func TestJWTManager_RefreshToken_ExpiredToken(t *testing.T) {
	secretKey := "test-secret-key"
	expiration := time.Millisecond // Very short expiration
	manager := NewJWTManager(secretKey, expiration)

	user := &User{
		Username:       "testuser",
		ServiceAccount: "test-sa",
		Namespace:      "default",
	}

	token, _, err := manager.GenerateToken(user)
	if err != nil {
		t.Fatalf("failed to generate token: %v", err)
	}

	// Wait for token to expire beyond grace period
	time.Sleep(time.Hour + time.Minute)

	_, _, err = manager.RefreshToken(token)
	if err == nil {
		t.Errorf("expected error for expired token beyond grace period")
		return
	}

	if err != ErrTokenExpired {
		t.Errorf("expected ErrTokenExpired, got %v", err)
	}
}

func TestJWTManager_GetSetTokenExpiration(t *testing.T) {
	secretKey := "test-secret-key"
	expiration := time.Hour
	manager := NewJWTManager(secretKey, expiration)

	if manager.GetTokenExpiration() != expiration {
		t.Errorf("expected expiration %v, got %v", expiration, manager.GetTokenExpiration())
	}

	newExpiration := time.Hour * 2
	manager.SetTokenExpiration(newExpiration)

	if manager.GetTokenExpiration() != newExpiration {
		t.Errorf("expected expiration %v, got %v", newExpiration, manager.GetTokenExpiration())
	}
}

func TestJWTClaims_Validation(t *testing.T) {
	secretKey := "test-secret-key"
	expiration := time.Hour
	manager := NewJWTManager(secretKey, expiration)

	user := &User{
		Username:       "testuser",
		ServiceAccount: "test-sa",
		Namespace:      "default",
	}

	token, _, err := manager.GenerateToken(user)
	if err != nil {
		t.Fatalf("failed to generate token: %v", err)
	}

	claims, err := manager.ValidateToken(token)
	if err != nil {
		t.Fatalf("failed to validate token: %v", err)
	}

	// Verify all claims are present and correct
	if claims.Username == "" {
		t.Errorf("username claim is empty")
	}

	if claims.ServiceAccount == "" {
		t.Errorf("service account claim is empty")
	}

	if claims.Namespace == "" {
		t.Errorf("namespace claim is empty")
	}

	if claims.IssuedAt == 0 {
		t.Errorf("issued at claim is zero")
	}

	if claims.ExpiresAt == 0 {
		t.Errorf("expires at claim is zero")
	}

	if claims.ExpiresAt <= claims.IssuedAt {
		t.Errorf("expires at should be after issued at")
	}
}
