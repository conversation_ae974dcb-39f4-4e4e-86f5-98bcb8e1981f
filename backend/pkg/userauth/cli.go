/*
Copyright 2025 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package userauth

import (
	"bufio"
	"fmt"
	"os"
	"strings"
	"syscall"
	"time"

	"golang.org/x/term"
	"gopkg.in/yaml.v2"
)

// GenerateUserConfig generates a user configuration file
func GenerateUserConfig(outputFile string) error {
	var users []User
	scanner := bufio.NewScanner(os.Stdin)

	fmt.Println("Headlamp User Configuration Generator")
	fmt.Println("=====================================")
	fmt.Println()

	for {
		user, err := promptForUser(scanner)
		if err != nil {
			return fmt.Errorf("failed to get user input: %w", err)
		}

		users = append(users, *user)

		fmt.Print("Add another user? (y/N): ")
		scanner.Scan()
		if strings.ToLower(strings.TrimSpace(scanner.Text())) != "y" {
			break
		}
		fmt.Println()
	}

	config := UserConfig{
		Users: users,
	}

	// Write to file
	data, err := yaml.Marshal(config)
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	err = os.WriteFile(outputFile, data, 0600)
	if err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	fmt.Printf("\nUser configuration written to: %s\n", outputFile)
	fmt.Printf("File permissions set to 0600 for security.\n")
	
	return nil
}

// promptForUser prompts for user information
func promptForUser(scanner *bufio.Scanner) (*User, error) {
	user := &User{
		Enabled:   true,
		CreatedAt: time.Now(),
	}

	// Username
	fmt.Print("Username: ")
	scanner.Scan()
	user.Username = strings.TrimSpace(scanner.Text())
	if user.Username == "" {
		return nil, fmt.Errorf("username cannot be empty")
	}

	// Password
	fmt.Print("Password: ")
	passwordBytes, err := term.ReadPassword(int(syscall.Stdin))
	if err != nil {
		return nil, fmt.Errorf("failed to read password: %w", err)
	}
	fmt.Println() // New line after password input

	password := string(passwordBytes)
	if len(password) < MinPasswordLength {
		return nil, fmt.Errorf("password must be at least %d characters", MinPasswordLength)
	}

	// Hash password
	hasher := NewBcryptHasher(DefaultBcryptCost)
	passwordHash, err := hasher.HashPassword(password)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}
	user.PasswordHash = passwordHash

	// Service Account
	fmt.Print("Service Account name: ")
	scanner.Scan()
	user.ServiceAccount = strings.TrimSpace(scanner.Text())
	if user.ServiceAccount == "" {
		return nil, fmt.Errorf("service account cannot be empty")
	}

	// Namespace
	fmt.Print("Namespace (default: default): ")
	scanner.Scan()
	user.Namespace = strings.TrimSpace(scanner.Text())
	if user.Namespace == "" {
		user.Namespace = "default"
	}

	// Description (optional)
	fmt.Print("Description (optional): ")
	scanner.Scan()
	user.Description = strings.TrimSpace(scanner.Text())

	return user, nil
}

// AddUserToConfig adds a user to an existing configuration file
func AddUserToConfig(configFile string, user *User) error {
	var config UserConfig

	// Try to read existing config
	if data, err := os.ReadFile(configFile); err == nil {
		if err := yaml.Unmarshal(data, &config); err != nil {
			return fmt.Errorf("failed to parse existing config: %w", err)
		}
	}

	// Check if user already exists
	for _, existingUser := range config.Users {
		if existingUser.Username == user.Username {
			return fmt.Errorf("user %s already exists", user.Username)
		}
	}

	// Add new user
	config.Users = append(config.Users, *user)

	// Write back to file
	data, err := yaml.Marshal(config)
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	err = os.WriteFile(configFile, data, 0600)
	if err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	return nil
}

// RemoveUserFromConfig removes a user from the configuration file
func RemoveUserFromConfig(configFile, username string) error {
	data, err := os.ReadFile(configFile)
	if err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	var config UserConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("failed to parse config: %w", err)
	}

	// Find and remove user
	found := false
	newUsers := make([]User, 0, len(config.Users))
	for _, user := range config.Users {
		if user.Username != username {
			newUsers = append(newUsers, user)
		} else {
			found = true
		}
	}

	if !found {
		return fmt.Errorf("user %s not found", username)
	}

	config.Users = newUsers

	// Write back to file
	data, err = yaml.Marshal(config)
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	err = os.WriteFile(configFile, data, 0600)
	if err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	return nil
}

// ListUsers lists all users in the configuration file
func ListUsers(configFile string) error {
	data, err := os.ReadFile(configFile)
	if err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	var config UserConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("failed to parse config: %w", err)
	}

	fmt.Printf("Users in %s:\n", configFile)
	fmt.Println("===========================================")
	
	if len(config.Users) == 0 {
		fmt.Println("No users found.")
		return nil
	}

	for _, user := range config.Users {
		status := "enabled"
		if !user.Enabled {
			status = "disabled"
		}

		fmt.Printf("Username: %s\n", user.Username)
		fmt.Printf("  Service Account: %s\n", user.ServiceAccount)
		fmt.Printf("  Namespace: %s\n", user.Namespace)
		fmt.Printf("  Status: %s\n", status)
		fmt.Printf("  Created: %s\n", user.CreatedAt.Format(time.RFC3339))
		if user.LastLoginAt != nil {
			fmt.Printf("  Last Login: %s\n", user.LastLoginAt.Format(time.RFC3339))
		}
		if user.Description != "" {
			fmt.Printf("  Description: %s\n", user.Description)
		}
		fmt.Println()
	}

	return nil
}

// ValidateConfig validates a user configuration file
func ValidateConfig(configFile string) error {
	data, err := os.ReadFile(configFile)
	if err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	var config UserConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("failed to parse config: %w", err)
	}

	usernames := make(map[string]bool)
	for i, user := range config.Users {
		// Check for duplicate usernames
		if usernames[user.Username] {
			return fmt.Errorf("duplicate username '%s' found", user.Username)
		}
		usernames[user.Username] = true

		// Validate user fields
		if user.Username == "" {
			return fmt.Errorf("user %d: username cannot be empty", i+1)
		}

		if user.PasswordHash == "" {
			return fmt.Errorf("user %s: password hash cannot be empty", user.Username)
		}

		if err := ParsePasswordHash(user.PasswordHash); err != nil {
			return fmt.Errorf("user %s: invalid password hash: %w", user.Username, err)
		}

		if user.ServiceAccount == "" {
			return fmt.Errorf("user %s: service account cannot be empty", user.Username)
		}

		if user.Namespace == "" {
			return fmt.Errorf("user %s: namespace cannot be empty", user.Username)
		}
	}

	fmt.Printf("Configuration file %s is valid.\n", configFile)
	fmt.Printf("Found %d user(s).\n", len(config.Users))

	return nil
}
