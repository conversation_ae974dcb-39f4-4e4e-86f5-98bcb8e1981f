/*
Copyright 2025 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package userauth

import (
	"context"
	"fmt"
	"sync"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
)

// ServiceAccountTokenManager manages service account tokens
type ServiceAccountTokenManager struct {
	clientset *kubernetes.Clientset
	cache     map[string]*ServiceAccountToken
	mutex     sync.RWMutex
}

// NewServiceAccountTokenManager creates a new service account token manager
func NewServiceAccountTokenManager(config *rest.Config) (*ServiceAccountTokenManager, error) {
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create kubernetes clientset: %w", err)
	}

	return &ServiceAccountTokenManager{
		clientset: clientset,
		cache:     make(map[string]*ServiceAccountToken),
	}, nil
}

// GetServiceAccountToken gets or creates a token for the specified service account
func (m *ServiceAccountTokenManager) GetServiceAccountToken(ctx context.Context, namespace, serviceAccountName string) (string, error) {
	cacheKey := fmt.Sprintf("%s/%s", namespace, serviceAccountName)

	// Check cache first
	m.mutex.RLock()
	if token, exists := m.cache[cacheKey]; exists {
		// Check if token is still valid (not expired)
		if time.Now().Before(token.ExpiresAt.Add(-5 * time.Minute)) { // 5 minute buffer
			m.mutex.RUnlock()
			return token.Token, nil
		}
	}
	m.mutex.RUnlock()

	// Token not in cache or expired, get a new one
	token, err := m.createServiceAccountToken(ctx, namespace, serviceAccountName)
	if err != nil {
		return "", err
	}

	// Cache the token
	m.mutex.Lock()
	m.cache[cacheKey] = token
	m.mutex.Unlock()

	return token.Token, nil
}

// createServiceAccountToken creates a new token for the service account
func (m *ServiceAccountTokenManager) createServiceAccountToken(ctx context.Context, namespace, serviceAccountName string) (*ServiceAccountToken, error) {
	// First, check if the service account exists
	_, err := m.clientset.CoreV1().ServiceAccounts(namespace).Get(ctx, serviceAccountName, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get service account %s/%s: %w", namespace, serviceAccountName, err)
	}

	// Create a token request for the service account
	tokenRequest := &corev1.TokenRequest{
		Spec: corev1.TokenRequestSpec{
			// Set token expiration to 24 hours
			ExpirationSeconds: func() *int64 { i := int64(24 * 60 * 60); return &i }(),
		},
	}

	tokenResponse, err := m.clientset.CoreV1().ServiceAccounts(namespace).CreateToken(
		ctx, serviceAccountName, tokenRequest, metav1.CreateOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to create token for service account %s/%s: %w", namespace, serviceAccountName, err)
	}

	return &ServiceAccountToken{
		Token:     tokenResponse.Status.Token,
		ExpiresAt: tokenResponse.Status.ExpirationTimestamp.Time,
		Namespace: namespace,
		Name:      serviceAccountName,
	}, nil
}

// ValidateServiceAccount checks if the service account exists and is valid
func (m *ServiceAccountTokenManager) ValidateServiceAccount(ctx context.Context, namespace, serviceAccountName string) error {
	sa, err := m.clientset.CoreV1().ServiceAccounts(namespace).Get(ctx, serviceAccountName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("service account %s/%s not found: %w", namespace, serviceAccountName, err)
	}

	// Check if service account is not disabled
	if sa.Annotations != nil {
		if disabled, exists := sa.Annotations["headlamp.io/disabled"]; exists && disabled == "true" {
			return fmt.Errorf("service account %s/%s is disabled", namespace, serviceAccountName)
		}
	}

	return nil
}

// ClearCache clears the token cache
func (m *ServiceAccountTokenManager) ClearCache() {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.cache = make(map[string]*ServiceAccountToken)
}

// ClearExpiredTokens removes expired tokens from cache
func (m *ServiceAccountTokenManager) ClearExpiredTokens() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	now := time.Now()
	for key, token := range m.cache {
		if now.After(token.ExpiresAt) {
			delete(m.cache, key)
		}
	}
}

// GetCachedTokenInfo returns information about cached tokens (for debugging)
func (m *ServiceAccountTokenManager) GetCachedTokenInfo() map[string]time.Time {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	info := make(map[string]time.Time)
	for key, token := range m.cache {
		info[key] = token.ExpiresAt
	}
	return info
}

// StartCleanupRoutine starts a background routine to clean up expired tokens
func (m *ServiceAccountTokenManager) StartCleanupRoutine(ctx context.Context, interval time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			m.ClearExpiredTokens()
		}
	}
}

// CreateServiceAccountIfNotExists creates a service account if it doesn't exist
func (m *ServiceAccountTokenManager) CreateServiceAccountIfNotExists(ctx context.Context, namespace, serviceAccountName string) error {
	// Check if service account already exists
	_, err := m.clientset.CoreV1().ServiceAccounts(namespace).Get(ctx, serviceAccountName, metav1.GetOptions{})
	if err == nil {
		// Service account already exists
		return nil
	}

	// Create the service account
	sa := &corev1.ServiceAccount{
		ObjectMeta: metav1.ObjectMeta{
			Name:      serviceAccountName,
			Namespace: namespace,
			Annotations: map[string]string{
				"headlamp.io/created-by": "headlamp-userauth",
				"headlamp.io/created-at": time.Now().Format(time.RFC3339),
			},
		},
	}

	_, err = m.clientset.CoreV1().ServiceAccounts(namespace).Create(ctx, sa, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("failed to create service account %s/%s: %w", namespace, serviceAccountName, err)
	}

	return nil
}
