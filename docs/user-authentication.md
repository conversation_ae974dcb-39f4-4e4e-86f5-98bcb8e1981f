# Headlamp User Authentication

This document describes how to configure and use the username/password authentication system in Headlamp that binds users to Kubernetes ServiceAccounts.

## Overview

The user authentication system allows you to:
- Authenticate users with username and password
- Bind each user to a specific Kubernetes ServiceAccount
- Control access through Kubernetes RBAC
- Maintain audit trails of user actions

## Architecture

```
User Login (username/password) 
    ↓
Headlamp Backend Validation
    ↓
JWT Session Token Generation
    ↓
ServiceAccount Token Retrieval
    ↓
Kubernetes API Access with SA Token
```

## Configuration

### 1. Enable User Authentication

Start Headlamp with user authentication enabled:

```bash
./headlamp-server \
  --enable-user-auth \
  --user-auth-config-file=/etc/headlamp/users.yaml \
  --user-auth-jwt-secret="your-secret-key" \
  --user-auth-token-expiration=24h
```

Or using environment variables:

```bash
export HEADLAMP_CONFIG_ENABLE_USER_AUTH=true
export HEADLAMP_CONFIG_USER_AUTH_CONFIG_FILE=/etc/headlamp/users.yaml
export HEADLAMP_CONFIG_USER_AUTH_JWT_SECRET=your-secret-key
export HEADLAMP_CONFIG_USER_AUTH_TOKEN_EXPIRATION=24h
./headlamp-server
```

### 2. Create User Configuration

Use the `userpass-config-gen` tool to create and manage users:

```bash
# Generate a new user configuration file
./userpass-config-gen --action=generate --config=/etc/headlamp/users.yaml

# Add a new user to existing configuration
./userpass-config-gen --action=add --username=newuser --config=/etc/headlamp/users.yaml

# List all users
./userpass-config-gen --action=list --config=/etc/headlamp/users.yaml

# Remove a user
./userpass-config-gen --action=remove --username=olduser --config=/etc/headlamp/users.yaml

# Validate configuration
./userpass-config-gen --action=validate --config=/etc/headlamp/users.yaml
```

### 3. Set Up Kubernetes ServiceAccounts and RBAC

Create ServiceAccounts and bind them to appropriate roles:

```yaml
# Admin ServiceAccount
apiVersion: v1
kind: ServiceAccount
metadata:
  name: headlamp-admin
  namespace: kube-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: headlamp-admin
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
- kind: ServiceAccount
  name: headlamp-admin
  namespace: kube-system
---
# Developer ServiceAccount
apiVersion: v1
kind: ServiceAccount
metadata:
  name: headlamp-developer
  namespace: default
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: headlamp-developer
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: edit
subjects:
- kind: ServiceAccount
  name: headlamp-developer
  namespace: default
---
# Viewer ServiceAccount
apiVersion: v1
kind: ServiceAccount
metadata:
  name: headlamp-viewer
  namespace: default
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: headlamp-viewer
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: view
subjects:
- kind: ServiceAccount
  name: headlamp-viewer
  namespace: default
```

## User Configuration File Format

The user configuration file (`users.yaml`) has the following format:

```yaml
users:
  - username: admin
    passwordHash: $2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL9B8L3Sm
    serviceAccount: headlamp-admin
    namespace: kube-system
    enabled: true
    description: "System administrator"
    createdAt: 2025-01-01T00:00:00Z
    
  - username: developer
    passwordHash: $2a$12$9y8Nd2j3kL4mN5oP6qR7sT8uV9wX0yZ1aB2cD3eF4gH5iJ6kL7mN8o
    serviceAccount: headlamp-developer
    namespace: default
    enabled: true
    description: "Developer with edit access"
    createdAt: 2025-01-01T00:00:00Z
```

### Fields Description

- `username`: Login username (required)
- `passwordHash`: bcrypt hash of the password (required)
- `serviceAccount`: Kubernetes ServiceAccount name (required)
- `namespace`: Namespace where the ServiceAccount exists (required)
- `enabled`: Whether the user account is active (default: true)
- `description`: Optional description of the user
- `createdAt`: When the user was created
- `lastLoginAt`: When the user last logged in (updated automatically)

## API Endpoints

The authentication system provides the following API endpoints:

### POST /api/auth/login
Authenticate a user with username and password.

**Request:**
```json
{
  "username": "admin",
  "password": "admin123"
}
```

**Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresAt": "2025-01-02T00:00:00Z",
  "user": {
    "username": "admin",
    "serviceAccount": "headlamp-admin",
    "namespace": "kube-system"
  }
}
```

### POST /api/auth/refresh
Refresh an existing JWT token.

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresAt": "2025-01-02T00:00:00Z"
}
```

### POST /api/auth/logout
Logout the current user (clears cookies).

### GET /api/auth/status
Get authentication status and user information.

**Response:**
```json
{
  "enabled": true,
  "authenticated": true,
  "user": {
    "username": "admin",
    "serviceAccount": "headlamp-admin",
    "namespace": "kube-system"
  }
}
```

## Security Considerations

1. **File Permissions**: Set restrictive permissions on the user configuration file:
   ```bash
   chmod 600 /etc/headlamp/users.yaml
   chown headlamp:headlamp /etc/headlamp/users.yaml
   ```

2. **JWT Secret**: Use a strong, randomly generated JWT secret:
   ```bash
   openssl rand -base64 32
   ```

3. **Password Policy**: Enforce strong passwords (minimum 8 characters).

4. **Token Expiration**: Set appropriate token expiration times (default: 24 hours).

5. **HTTPS**: Always use HTTPS in production to protect credentials in transit.

6. **Regular Rotation**: Regularly rotate passwords and JWT secrets.

## Testing

### Manual Testing

1. Start Headlamp with user authentication enabled
2. Navigate to the login page
3. Enter valid credentials
4. Verify access to Kubernetes resources based on ServiceAccount permissions

### API Testing

```bash
# Login
curl -X POST http://localhost:4466/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# Use the returned token for subsequent requests
curl -X GET http://localhost:4466/api/clusters/default/api/v1/pods \
  -H "Authorization: Bearer <token>"
```

## Troubleshooting

### Common Issues

1. **Authentication Failed**: Check username/password and ensure user is enabled
2. **ServiceAccount Not Found**: Verify ServiceAccount exists in the specified namespace
3. **Permission Denied**: Check RBAC bindings for the ServiceAccount
4. **Token Expired**: Refresh the token or login again
5. **Invalid Configuration**: Validate the user configuration file

### Logs

Enable debug logging to troubleshoot issues:
```bash
export HEADLAMP_LOG_LEVEL=debug
./headlamp-server --enable-user-auth
```

### Configuration Validation

```bash
./userpass-config-gen --action=validate --config=/etc/headlamp/users.yaml
```

## Migration from Token-based Authentication

If you're migrating from direct token-based authentication:

1. Create ServiceAccounts for existing users
2. Set up appropriate RBAC bindings
3. Generate user configuration with the `userpass-config-gen` tool
4. Enable user authentication in Headlamp
5. Inform users of their new login credentials

## Best Practices

1. Use descriptive usernames and ServiceAccount names
2. Follow the principle of least privilege for RBAC
3. Regularly audit user access and permissions
4. Monitor authentication logs for suspicious activity
5. Implement password rotation policies
6. Use namespace-specific ServiceAccounts when possible
7. Document user roles and responsibilities
